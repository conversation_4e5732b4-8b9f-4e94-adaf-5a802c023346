'use client'

import { ThemeProvider } from 'next-themes'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useToast } from '@/hooks/use-toast'
import { useEffect } from 'react'

// 创建 QueryClient 实例
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5分钟
    },
  },
})

// 认证错误处理组件
function AuthErrorHandler() {
  const { toast } = useToast()

  useEffect(() => {
    // 监听认证过期事件
    const handleAuthExpired = (event: CustomEvent) => {
      toast({
        title: "🔒 登录已过期1",
        description: event.detail.message || "请重新登录以继续使用",
        variant: "destructive",
        duration: 5000,
      })
    }

    // 监听认证错误事件
    const handleAuthError = (event: CustomEvent) => {
      toast({
        title: "❌ 认证失败",
        description: event.detail.message || "请重新登录",
        variant: "destructive",
        duration: 5000,
      })
    }

    // 添加事件监听器
    window.addEventListener('auth:expired', handleAuthExpired as EventListener)
    window.addEventListener('auth:error', handleAuthError as EventListener)

    // 清理函数
    return () => {
      window.removeEventListener('auth:expired', handleAuthExpired as EventListener)
      window.removeEventListener('auth:error', handleAuthError as EventListener)
    }
  }, [toast])

  return null // 这个组件不渲染任何内容
}

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider
        attribute="class" // 主题属性应用方式
        defaultTheme="system" // 默认主题
        enableSystem // 是否启用系统主题
        disableTransitionOnChange // 是否禁用主题切换动画
      >
        {children}
      </ThemeProvider>
      <AuthErrorHandler />
    </QueryClientProvider>
  )
} 