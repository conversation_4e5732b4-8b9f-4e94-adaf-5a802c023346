import '@testing-library/jest-dom';
import { configure } from '@testing-library/react';

// 配置测试库
configure({
  asyncUtilTimeout: 3000, // 增加异步操作超时时间
});

// 扩展Jest的匹配器
expect.extend({
  toHaveBeenCalledOnceWith(received, ...args) {
    const pass = received.mock.calls.length === 1 &&
      JSON.stringify(received.mock.calls[0]) === JSON.stringify(args);
    
    return {
      pass,
      message: () => pass
        ? `期望函数没有被调用一次且参数为 ${JSON.stringify(args)}`
        : `期望函数被调用一次且参数为 ${JSON.stringify(args)}`,
    };
  },
}); 