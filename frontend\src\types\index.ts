// 用户类型
export interface User {
  id: string
  email: string
  name: string
  avatar?: string
  created_at: string
  updated_at: string
}

// 笔记类型
export interface Note {
  id: string
  title: string
  content: string
  tags: string[]
  user_id: string
  created_at: string
  updated_at: string
}

// API响应类型
export interface ApiResponse<T = any> {
  status: 'success' | 'error'
  data?: T
  message?: string
  error?: {
    code: string
    details?: any
  }
}

// 分页类型
export interface Pagination {
  total: number
  page: number
  limit: number
  total_pages: number
}

// 笔记列表响应类型
export interface NotesResponse {
  notes: Note[]
  pagination: Pagination
} 