import { useAuthStore } from './auth';
import { User } from '@/types';

// 获取初始状态的辅助函数，因为 store 的创建是立即执行的
const getInitialState = () => useAuthStore.getState();

describe('useAuthStore', () => {
  const mockUser: User = {
    id: '1',
    email: '<EMAIL>',
    name: 'Test User',
    // created_at 和 updated_at 可以根据需要添加，如果 User 类型需要
  };

  beforeEach(() => {
    // 重置 store到初始状态
    // Zustand 的 reset 方法不是标准 API，通常的做法是重新设置初始值或使用 logout 等 action
    // 对于 persist middleware，清除 localStorage (如果模拟了) 也是一种方法
    // 这里我们用 logout action 来确保一个干净的状态，或者直接 set 回初始值
    useAuthStore.setState(getInitialState()); // 重置为"真实"的初始状态
  });

  it('应该有正确的初始状态', () => {
    const state = useAuthStore.getState();
    expect(state.user).toBeNull();
    expect(state.token).toBeNull();
    expect(state.refreshToken).toBeNull();
    expect(state.isAuthenticated).toBe(false);
  });

  describe('setUser', () => {
    it('设置用户时应该更新 user 和 isAuthenticated', () => {
      useAuthStore.getState().setUser(mockUser);
      const state = useAuthStore.getState();
      expect(state.user).toEqual(mockUser);
      expect(state.isAuthenticated).toBe(true);
    });

    it('设置用户为 null 时应该清除 user 和 isAuthenticated', () => {
      // 先设置一个用户
      useAuthStore.getState().setUser(mockUser);
      // 然后设置回 null
      useAuthStore.getState().setUser(null);
      const state = useAuthStore.getState();
      expect(state.user).toBeNull();
      expect(state.isAuthenticated).toBe(false);
    });
  });

  describe('setTokens', () => {
    it('应该更新 token 和 refreshToken', () => {
      const newToken = 'new-access-token';
      const newRefreshToken = 'new-refresh-token';
      useAuthStore.getState().setTokens(newToken, newRefreshToken);
      const state = useAuthStore.getState();
      expect(state.token).toBe(newToken);
      expect(state.refreshToken).toBe(newRefreshToken);
    });

    it('设置 token 为 null 时也应该正确工作', () => {
      useAuthStore.getState().setTokens(null, null);
      const state = useAuthStore.getState();
      expect(state.token).toBeNull();
      expect(state.refreshToken).toBeNull();
    });
  });

  describe('logout', () => {
    it('应该清除 user, tokens 并设置 isAuthenticated 为 false', () => {
      // 先设置一些状态
      useAuthStore.getState().setUser(mockUser);
      useAuthStore.getState().setTokens('some-token', 'some-refresh-token');
      
      // 执行 logout
      useAuthStore.getState().logout();
      const state = useAuthStore.getState();

      expect(state.user).toBeNull();
      expect(state.token).toBeNull();
      expect(state.refreshToken).toBeNull();
      expect(state.isAuthenticated).toBe(false);
    });

    it('即使在初始状态下调用 logout 也应该保持一致性', () => {
      useAuthStore.getState().logout();
      const state = useAuthStore.getState();
      expect(state.user).toBeNull();
      expect(state.token).toBeNull();
      expect(state.refreshToken).toBeNull();
      expect(state.isAuthenticated).toBe(false);
    });
  });
}); 