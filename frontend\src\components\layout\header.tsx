'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { LogOut, User, Settings, BookOpen, LogIn, Loader2 } from 'lucide-react'  //用作头像定义
import { useToast } from '@/hooks/use-toast'
import { useAuth } from '@/hooks/use-auth'

export function Header() {
  const router = useRouter()
  const { toast } = useToast()
  const { user, isAuthenticated, isLoading, logout } = useAuth()
  const [isLoggingOut, setIsLoggingOut] = useState(false)

  const handleLogout = async () => {
    try {
      setIsLoggingOut(true)
      
      toast({
        title: '👋 已退出登录',
        description: '感谢您的使用，再见！',
      })
      
      // 使用useAuth的logout方法
      logout()
    } catch (error) {
      console.error('退出登录失败:', error)
      toast({
        title: '❌ 退出失败',
        description: '退出登录时出现错误',
        variant: 'destructive',
      })
    } finally {
      setIsLoggingOut(false)
    }
  }

  const handleLogin = () => {
    router.push('/login')
  }

  const getUserInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const getUserDisplayName = (user: any) => {
    return user?.name || user?.email?.split('@')[0] || '用户'
  }

  return (
    <header className="sticky top-0 z-50 w-full border-b border-gray-200/50 dark:border-gray-700/50 bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl">
      <div className="container mx-auto px-4 py-4 max-w-7xl">
        <div className="flex items-center justify-between">
          {/* Logo和标题 */}
          <Link href={isAuthenticated ? '/notes' : '/'} className="flex items-center gap-3 hover:opacity-80 transition-opacity">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
              <BookOpen className="w-6 h-6 text-white" />
            </div>
            <div className="hidden sm:block">
              <h1 className="text-xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">
                我的笔记
              </h1>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                记录每一个想法
              </p>
            </div>
          </Link>

          {/* 用户区域 */}
          <div className="flex items-center gap-4">
            {isAuthenticated ? (
              isLoading ? (
                // 加载状态  其中 Loader2 是头像定义
                <div className="flex items-center gap-2">
                  <Loader2 className="w-4 h-4 animate-spin text-gray-500" />  
                  <span className="text-sm text-gray-500">加载中...</span>
                </div>
              ) : user ? (
                // 已登录状态
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="relative h-10 w-10 rounded-full">
                      <Avatar className="h-10 w-10 ring-2 ring-blue-500/20 hover:ring-blue-500/40 transition-all">
                        <AvatarImage src={user.avatar || undefined} alt={getUserDisplayName(user)} />
                        <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white font-medium">
                          {getUserInitials(getUserDisplayName(user))}
                        </AvatarFallback>
                      </Avatar>
                    </Button>
                  </DropdownMenuTrigger>
                  
                  <DropdownMenuContent className="w-56" align="end" forceMount>
                    <DropdownMenuLabel className="font-normal">
                      <div className="flex flex-col space-y-1">
                        <p className="text-sm font-medium leading-none">{getUserDisplayName(user)}</p>
                        <p className="text-xs leading-none text-muted-foreground">
                          {user.email}
                        </p>
                      </div>
                    </DropdownMenuLabel>
                    
                    <DropdownMenuSeparator />
                    
                    <DropdownMenuItem asChild>
                      <Link href="/notes" className="w-full cursor-pointer">
                        <BookOpen className="mr-2 h-4 w-4" />
                        <span>我的笔记</span>
                      </Link>
                    </DropdownMenuItem>
                    
                    <DropdownMenuItem disabled>
                      <User className="mr-2 h-4 w-4" />
                      <span>个人资料</span>
                    </DropdownMenuItem>
                    
                    <DropdownMenuItem disabled>
                      <Settings className="mr-2 h-4 w-4" />
                      <span>设置</span>
                    </DropdownMenuItem>
                    
                    <DropdownMenuSeparator />
                    
                    <DropdownMenuItem 
                      className="text-red-600 dark:text-red-400 cursor-pointer"
                      onClick={handleLogout}
                      disabled={isLoggingOut}
                    >
                      <LogOut className="mr-2 h-4 w-4" />
                      <span>{isLoggingOut ? '退出中...' : '退出登录'}</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              ) : (
                // 认证失败或无用户信息
                <Button
                  onClick={handleLogin}
                  variant="outline"
                  className="text-red-600 border-red-200 hover:bg-red-50"
                >
                  <LogIn className="mr-2 h-4 w-4" />
                  重新登录
                </Button>
              )
            ) : (
              // 未登录状态
              <Button
                onClick={handleLogin}
                className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-200"
              >
                <LogIn className="mr-2 h-4 w-4" />
                登录
              </Button>
            )}
          </div>
        </div>
      </div>
    </header>
  )
} 