const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_KEY
);

/**
 * Supabase 认证中间件
 * 用于验证请求中的认证令牌并获取用户信息
 */
const authMiddleware = async (req, res, next) => {
    try {
        // 从请求头中获取 Authorization 字段
        const authHeader = req.headers.authorization;
        // 如果没有 Authorization 头，返回 401 错误
        if (!authHeader) {
            return res.status(401).json({
                status: 'error',
                message: '无效的认证令牌'
            });
        }

        // 从 Authorization 头中提取 token（格式：Bearer <token>）
        const token = authHeader.split(' ')[1];
        // 如果无法提取到 token，返回 401 错误
        if (!token) {
            return res.status(401).json({
                status: 'error',
                message: '无效的认证令牌格式'
            });
        }

        // 使用 Supabase 验证 token 并获取用户信息
        const { data: { user }, error } = await supabase.auth.getUser(token);

        // 如果验证出错或没有找到用户，返回 401 错误
        if (error || !user) {
            return res.status(401).json({
                status: 'error',
                message: '无效的认证令牌'
            });
        }

        // 将用户信息添加到请求对象中，供后续中间件和路由处理器使用
        req.user = user;
        // 调用下一个中间件或路由处理器
        next();
    } catch (error) {
        // 捕获并记录任何运行时错误
        console.error('认证中间件错误:', error);
        // 返回 401 错误响应
        res.status(401).json({
            status: 'error',
            message: '认证失败'
        });
    }
};

/**
 * 可选的JWT认证中间件
 * 如果提供了有效的token，会加载用户信息，但不会阻止未认证的访问
 */
const optionalAuth = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        if (authHeader && authHeader.startsWith('Bearer ')) {
            const token = authHeader.split(' ')[1];
            const decoded = verifyToken(token);
            const user = await UserModel.findById(decoded.id);
            if (user) {
                req.user = user;
            }
        }
        next();
    } catch (error) {
        // 即使token无效也继续处理请求
        next();
    }
};

module.exports = {
    authMiddleware,
    optionalAuth
}; 