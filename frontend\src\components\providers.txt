
这个 useEffect 主要用于设置和清理事件监听器，这些操作应该只在组件挂载和卸载时执行一次
toast 函数来自 useToast hook，它应该是稳定的（不会随渲染改变），不需要作为依赖项
将 toast 放入依赖数组可能导致不必要的重新订阅事件


function AuthErrorHandler() {
  const { toast } = useToast()
  
  // 将 toast 处理逻辑提取出来，用 useCallback 包装
  const handleAuthExpired = useCallback((event: CustomEvent) => {
    toast({
      title: "🔒 登录已过期",
      description: event.detail.message || "请重新登录以继续使用",
      variant: "destructive",
      duration: 5000,
    })
  }, [toast])

  const handleAuthError = useCallback((event: CustomEvent) => {
    toast({
      title: "❌ 认证失败",
      description: event.detail.message || "请重新登录",
      variant: "destructive",
      duration: 5000,
    })
  }, [toast])

  useEffect(() => {
    window.addEventListener('auth:expired', handleAuthExpired as EventListener)
    window.addEventListener('auth:error', handleAuthError as EventListener)

    return () => {
      window.removeEventListener('auth:expired', handleAuthExpired as EventListener)
      window.removeEventListener('auth:error', handleAuthError as EventListener)
    }
  }, [handleAuthExpired, handleAuthError]) // 依赖这些回调函数
}