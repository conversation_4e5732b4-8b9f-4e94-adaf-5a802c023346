const express = require('express');


//使用 Express Validator（一个用于 Node.js 和 Express 框架的验证中间件）\
// 来定义对 HTTP 请求的 body 中 tags 字段的验证规则。
 

const { body, query, param } = require('express-validator');
const NotesController = require('../controllers/notes.controller');
const { authMiddleware } = require('../middlewares/auth.middleware');
const { createClient } = require('@supabase/supabase-js');
const { validationResult } = require('express-validator');
const NoteModel = require('../models/note.model');

const router = express.Router();

// 创建 Supabase 客户端
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// 中间件：验证请求
const validateRequest = async (req, res, next) => {
    console.log('=== 请求验证开始 ===');
    console.log('请求头:', JSON.stringify(req.headers, null, 2));
    console.log('请求体:', JSON.stringify(req.body, null, 2));

    const authHeader = req.headers.authorization;
    if (!authHeader) {
        console.log('错误: 未找到 Authorization 头');
        return res.status(401).json({ status: 'error', message: '未提供认证信息' });
    }

    // 解析 token
    const token = authHeader.split(' ')[1];
    console.log('Token:', token);

    try {
        // 直接使用 token 创建新的 Supabase 客户端  初始化一个客户端
        const authenticatedSupabase = createClient(
            supabaseUrl,
            supabaseKey,
            {
                auth: {
                    autoRefreshToken: false,
                    persistSession: false,
                    detectSessionInUrl: false
                },
                global: {
                    headers: {
                        Authorization: `Bearer ${token}`
                    }
                }
            }
        );

        // 验证用户
        const { data: { user }, error } = await authenticatedSupabase.auth.getUser();

        if (error) {
            console.error('验证用户失败:', error);
            return res.status(401).json({ status: 'error', message: '无效的认证信息' });
        }

        if (!user) {
            console.error('未找到用户信息');
            return res.status(401).json({ status: 'error', message: '未认证的用户' });
        }

        // 将认证后的 Supabase 客户端和用户信息添加到请求对象
        // 将认证后的客户端实例添加到请求对象中，方便后续的中间件或路由处理函数使用
        req.supabase = authenticatedSupabase;
        req.user = user;
        next();
    } catch (error) {
        console.error('认证过程出错:', error);
        res.status(401).json({ status: 'error', message: '认证失败' });
    }
};

// 验证规则
const createNoteValidation = [
    body('title')  // 验证链
        .trim()
        .notEmpty()
        .withMessage('笔记标题不能为空')
        .isLength({ max: 255 })
        .withMessage('笔记标题不能超过255个字符'),
    body('content')
        .optional()
        .isString()
        .withMessage('笔记内容必须是字符串'),
    body('tags')
        .optional()
        .isArray()
        .withMessage('标签必须是数组格式')
        .custom(tags => {
            if (tags && tags.some(tag => typeof tag !== 'string')) {
                throw new Error('标签必须是字符串');
            }
            if (tags && tags.some(tag => tag.length > 50)) {
                throw new Error('单个标签长度不能超过50个字符');
            }
            return true;
        })
];

const updateNoteValidation = [
    param('id')
        .isUUID()
        .withMessage('无效的笔记ID'),
    body('title')
        .optional()
        .trim()
        .notEmpty()
        .withMessage('笔记标题不能为空')
        .isLength({ max: 255 })
        .withMessage('笔记标题不能超过255个字符'),
    body('content')
        .optional()
        .isString()
        .withMessage('笔记内容必须是字符串'),
    body('tags')
        .optional()
        .isArray()
        .withMessage('标签必须是数组格式')
        .custom(tags => {
            if (tags && tags.some(tag => typeof tag !== 'string')) {
                throw new Error('标签必须是字符串');
            }
            if (tags && tags.some(tag => tag.length > 50)) {
                throw new Error('单个标签长度不能超过50个字符');
            }
            return true;
        })
];

const listNotesValidation = [
    query('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('页码必须是大于0的整数'),
    query('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('每页数量必须在1-100之间'),
    query('sortBy')
        .optional()
        .isIn(['created_at', 'updated_at', 'title'])
        .withMessage('无效的排序字段'),
    query('sortOrder')
        .optional()
        .isIn(['asc', 'desc'])
        .withMessage('排序方向必须是 asc 或 desc'),
    query('tag')
        .optional()
        .isString()
        .withMessage('标签必须是字符串'),
    query('search')
        .optional()
        .isString()
        .withMessage('搜索关键词必须是字符串')
];

const noteIdValidation = [
    param('id')
        .isUUID()
        .withMessage('无效的笔记ID')
];

const bulkDeleteValidation = [
    body('noteIds')
        .isArray()
        .withMessage('noteIds必须是数组格式')
        .custom(noteIds => {
            if (noteIds.length === 0) {
                throw new Error('noteIds不能为空');
            }
            if (noteIds.some(id => !isUUID(id))) {
                throw new Error('包含无效的笔记ID');
            }
            return true;
        })
];

// 所有路由都需要认证
router.use(authMiddleware);

// POST 创建新笔记
router.post('/', [
    ...createNoteValidation,
    // 验证结果处理中间件
    (req, res, next) => {
        const errors = validationResult(req);  //  withMessage 存在 validationResult 中
        if (!errors.isEmpty()) {
            return res.status(400).json({
                status: 'error',
                errors: errors.array()
            });
        }
        next();
    }
], async (req, res) => {
    try {
        const note = await NoteModel.create(req.body, req.user.id);
        res.status(201).json({
            status: 'success',
            data: note
        });
    } catch (error) {
        console.error('创建笔记错误:', error);
        res.status(500).json({
            status: 'error',
            message: '创建笔记失败'
        });
    }
});

router.get('/', validateRequest, async (req, res) => {
    try {
        console.log('=== 获取笔记列表 ===');
        console.log('当前用户:', req.user);

        // 获取分页参数
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const offset = (page - 1) * limit;

        // 使用请求特定的 Supabase 客户端
        const { data, error, count } = await req.supabase
            .from('notes')
            .select('*', { count: 'exact' })
            .eq('user_id', req.user.id)
            .order('created_at', { ascending: false })
            .range(offset, offset + limit - 1);

        console.log('查询结果:', { data, error, count });

        if (error) {
            console.error('获取笔记失败:', error);
            throw error;
        }

        res.json({
            status: 'success',
            data: {
                notes: data || [],
                pagination: {
                    total: count,
                    page: page,
                    limit: limit,
                    total_pages: Math.ceil(count / limit)
                }
            }
        });
    } catch (error) {
        console.error('获取笔记失败:', error);
        res.status(500).json({
            status: 'error',
            message: error.message || 'Auth session missing!'
        });
    }
});

router.get('/:id', noteIdValidation, NotesController.getNoteById);
router.put('/:id', updateNoteValidation, NotesController.updateNote);
router.delete('/:id', noteIdValidation, NotesController.deleteNote);

// 标签相关路由
router.get('/tags', NotesController.getTagStats);

// 批量操作路由
router.post('/bulk-delete', bulkDeleteValidation, NotesController.bulkDeleteNotes);

// 特殊操作路由
router.post('/:id/duplicate', noteIdValidation, NotesController.duplicateNote);

// UUID验证辅助函数
function isUUID(str) {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(str);
}

module.exports = router; 