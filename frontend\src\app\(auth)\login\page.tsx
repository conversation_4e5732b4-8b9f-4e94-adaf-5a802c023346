import Link from 'next/link'
import { LoginForm } from '@/components/auth/login-form'
import { LogIn, Sparkles, ArrowRight, Home } from 'lucide-react'

export default function LoginPage() {
  return (
    <div className="space-y-8">
      {/* 页面标题区域 */}
      <div className="text-center space-y-4">
        {/* 图标 */}
        <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-green-500 to-blue-600 rounded-2xl shadow-lg mb-4">
          <LogIn className="w-8 h-8 text-white" />
        </div>
        
        {/* 标题和描述 */}
        <div className="space-y-2">
          <h1 className="text-3xl font-bold bg-gradient-to-r from-green-600 via-blue-600 to-purple-600 bg-clip-text text-transparent">
            欢迎回来
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            继续你的笔记之旅，管理你的想法和灵感
          </p>
        </div>
        
        {/* 装饰元素 */}
        <div className="flex items-center justify-center space-x-2 text-yellow-500">
          <Sparkles className="w-4 h-4 animate-pulse" />
          <span className="text-sm text-gray-500 dark:text-gray-400">安全登录，保护隐私</span>
          <Sparkles className="w-4 h-4 animate-pulse" />
        </div>
      </div>

      {/* 登录表单 */}
      <LoginForm />
      
      {/* 注册链接 */}
      <div className="text-center">
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-200 dark:border-gray-700"></div>
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-white dark:bg-gray-900 px-2 text-gray-500 dark:text-gray-400">
              还没有账号？
            </span>
          </div>
        </div>
        
        <div className="mt-6">
          <Link 
            href="/register" 
            className="group inline-flex items-center gap-2 text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300 font-medium transition-colors duration-200"
          >
            <span>立即注册，开始使用</span>
            <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-200" />
          </Link>
        </div>
      </div>
    </div>
  )
} 