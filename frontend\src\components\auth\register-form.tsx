'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'  //  用于 typescript  数据验证库
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { setToken, setRefreshToken } from '@/lib/cookies'
import { axiosInstance } from '@/lib/axios' // 新增：导入已配置的axios实例
import { useToast } from '@/hooks/use-toast' // 新增：导入toast hook

// 添加类型定义
interface RegisterResponse {
  status: string
  data: {
    user: {
      id: string
      email: string
      name: string
      created_at: string
      updated_at: string
    }
    session: {
      access_token: string
      refresh_token: string
      token_type: string
      expires_in: number
      expires_at: number
      user: any
    }
    message: string
  }
}

const formSchema = z.object({
  name: z.string().min(2, {
    message: '名字至少需要2个字符',
  }),
  email: z.string().email({
    message: '请输入有效的邮箱地址',
  }),
  password: z.string().min(6, {
    message: '密码至少需要6个字符',
  }),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: '两次输入的密码不一致',
  path: ['confirmPassword'],
})

export function RegisterForm() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast() // 新增：初始化toast

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      email: '',
      password: '',
      confirmPassword: '',
    },
  })

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsLoading(true)

    try {
      console.log('🚀 开始注册请求...', { email: values.email, name: values.name })
      
      // axios拦截器返回response.data，所以我们直接得到API响应的body
      const response = await axiosInstance.post<RegisterResponse>('/auth/register', {
        name: values.name,
        email: values.email,
        password: values.password,
      })
      
      // 由于我们有axios拦截器，response就是API的响应体
      const apiResponse = response as unknown as RegisterResponse

      console.log('📦 收到注册响应 - 原始数据:', apiResponse)
      console.log('📦 响应类型:', typeof apiResponse)
      console.log('📦 响应是否为对象:', apiResponse && typeof apiResponse === 'object')
      console.log('📦 响应的所有键:', apiResponse ? Object.keys(apiResponse) : 'null')
      
      // 检查响应结构
      if (apiResponse && typeof apiResponse === 'object') {
        console.log('📦 status 属性:', apiResponse.status)
        console.log('📦 data 属性:', apiResponse.data)
        console.log('📦 session 属性:', apiResponse.data?.session)
        console.log('📦 access_token:', apiResponse.data?.session?.access_token)
        console.log('📦 refresh_token:', apiResponse.data?.session?.refresh_token)
      }

      // 检查响应格式和token - 修正token路径
      if (apiResponse && 
          apiResponse.status === 'success' && 
          apiResponse.data && 
          apiResponse.data.session &&
          apiResponse.data.session.access_token) {
        
        console.log('✅ 注册成功，准备保存token和跳转...')
        
        // token在apiResponse.data.session.access_token中
        setToken(apiResponse.data.session.access_token)
        // 如果有refresh_token也保存
        if (apiResponse.data.session.refresh_token) {
          setRefreshToken(apiResponse.data.session.refresh_token)
        }
        
        // 显示成功提示
        toast({
          title: "✨ 注册成功！",
          description: `欢迎 ${values.name}！正在跳转到笔记页面...`,
          variant: "default"
        })

        console.log('🎉 即将跳转到 /notes')
        
        // 延迟一下让用户看到成功提示
        setTimeout(() => {
          // 检查是否有保存的跳转路径
          const redirectPath = sessionStorage.getItem('redirectAfterLogin')
          if (redirectPath) {
            sessionStorage.removeItem('redirectAfterLogin')
            router.push(redirectPath)
          } else {
            router.push('/notes')
          }
        }, 1000)
        
      } else {
        console.log('❌ 响应格式检查失败:')
        console.log('   - 响应存在:', !!apiResponse)
        console.log('   - status值:', apiResponse?.status)
        console.log('   - status正确:', apiResponse?.status === 'success')
        console.log('   - data存在:', !!apiResponse?.data)
        console.log('   - session存在:', !!apiResponse?.data?.session)
        console.log('   - access_token存在:', !!apiResponse?.data?.session?.access_token)
        console.log('   - 完整响应:', JSON.stringify(apiResponse, null, 2))
        
        throw new Error(`响应格式异常: ${JSON.stringify(apiResponse)}`)
      }
    } catch (error: any) {
      console.error('💥 注册错误:', error)
      console.error('💥 错误详情:', {
        message: error.message,
        response: error.response,
        status: error.response?.status,
        data: error.response?.data
      })
      
      // 根据API.md的错误响应格式处理错误
      const errorMessage = error.response?.data?.message || 
                          error.response?.data?.errors?.[0]?.msg || 
                          error.message ||
                          "注册失败，请稍后重试"
      
      // 显示错误提示
      toast({
        title: "注册失败",
        description: errorMessage,
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-gray-700 dark:text-gray-300 font-medium">姓名</FormLabel>
              <FormControl>
                <Input 
                  placeholder="输入你的姓名" 
                  {...field} 
                  className="border-gray-200/50 dark:border-gray-700/50 bg-white/50 dark:bg-gray-900/50 backdrop-blur-sm focus:border-blue-500/50 focus:ring-blue-500/50 rounded-xl py-3 transition-all duration-200"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-gray-700 dark:text-gray-300 font-medium">邮箱</FormLabel>
              <FormControl>
                <Input 
                  placeholder="输入你的邮箱地址" 
                  type="email"
                  {...field} 
                  className="border-gray-200/50 dark:border-gray-700/50 bg-white/50 dark:bg-gray-900/50 backdrop-blur-sm focus:border-blue-500/50 focus:ring-blue-500/50 rounded-xl py-3 transition-all duration-200"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-gray-700 dark:text-gray-300 font-medium">密码</FormLabel>
              <FormControl>
                <Input 
                  type="password" 
                  placeholder="设置你的密码"
                  {...field} 
                  className="border-gray-200/50 dark:border-gray-700/50 bg-white/50 dark:bg-gray-900/50 backdrop-blur-sm focus:border-blue-500/50 focus:ring-blue-500/50 rounded-xl py-3 transition-all duration-200"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="confirmPassword"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-gray-700 dark:text-gray-300 font-medium">确认密码</FormLabel>
              <FormControl>
                <Input 
                  type="password" 
                  placeholder="再次输入密码"
                  {...field} 
                  className="border-gray-200/50 dark:border-gray-700/50 bg-white/50 dark:bg-gray-900/50 backdrop-blur-sm focus:border-blue-500/50 focus:ring-blue-500/50 rounded-xl py-3 transition-all duration-200"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button 
          type="submit" 
          disabled={isLoading}
          className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white rounded-xl py-3 font-medium shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? (
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
              <span>注册中...</span>
            </div>
          ) : (
            '创建账号'
          )}
        </Button>
      </form>
    </Form>
  )
} 