'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form' //  用于 表单验证
import { zodResolver } from '@hookform/resolvers/zod'  //  用于 typescript  数据验证库
import * as z from 'zod' //  用于 typescript  数据验证库
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { setToken, setRefreshToken } from '@/lib/cookies'
import { axiosInstance } from '@/lib/axios'
import { useToast } from '@/hooks/use-toast'

const formSchema = z.object({
  email: z.string().email({
    message: '请输入有效的邮箱地址',
  }),
  password: z.string().min(6, {
    message: '密码至少需要6个字符',
  }),
})

interface LoginResponse {
  status: string
  data: {
    user: {
      id: string
      email: string
      name: string
      created_at: string
      updated_at: string
    }
    session: {
      access_token: string
      refresh_token: string
      token_type: string
      expires_in: number
      expires_at: number
      user: any
    }
  }
}

export function LoginForm() {
  const router = useRouter()
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  })

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsLoading(true)

    try {
      console.log('🚀 开始登录请求...', { email: values.email })
      
      const response = await axiosInstance.post<LoginResponse>('/auth/login', {
        email: values.email,
        password: values.password,
      })
      
      const apiResponse = response as unknown as LoginResponse

      console.log('📦 收到登录响应:', apiResponse)

      if (apiResponse && 
          apiResponse.status === 'success' && 
          apiResponse.data && 
          apiResponse.data.session &&
          apiResponse.data.session.access_token) {
        
        console.log('✅ 登录成功，准备保存token和跳转...')
        
        setToken(apiResponse.data.session.access_token)
        if (apiResponse.data.session.refresh_token) {
          setRefreshToken(apiResponse.data.session.refresh_token)
        }
        
        toast({
          title: "✨ 登录成功！",
          description: `欢迎回来 ${apiResponse.data.user.name || apiResponse.data.user.email}！`,
          variant: "default"
        })

        console.log('🎉 即将跳转到 /notes')
        
        /* 
         * 登录成功后的页面跳转逻辑：
         * 
         * 1. 使用 setTimeout 延迟1秒执行跳转
         *    目的是让用户看到登录成功的提示
         * 
         * 2. 检查 sessionStorage 中是否存在之前保存的跳转路径：
         *    - redirectAfterLogin 存储了用户在登录前想要访问的页面路径
         * 
         * 3. 跳转逻辑：
         *    - 如果存在保存的路径：
         *      > 从 sessionStorage 中获取路径
         *      > 删除存储的路径（清理存储）
         *      > 跳转到之前保存的页面
         *    - 如果不存在保存的路径：
         *      > 默认跳转到笔记列表页面 (/notes)
         */
        setTimeout(() => {
          const redirectPath = sessionStorage.getItem('redirectAfterLogin')
          if (redirectPath) {
            sessionStorage.removeItem('redirectAfterLogin')
            router.push(redirectPath)
          } else {
            router.push('/notes')
          }
        }, 1000)

      } else {
        console.log('❌ 登录响应格式异常:', apiResponse)
        throw new Error('响应格式异常')
      }
    } catch (error: any) {
      console.error('登录错误:', error)
      
      const errorMessage = error.response?.data?.message || 
                          error.message ||
                          "登录失败，请稍后重试1"
      
      toast({
        title: "登录失败",
        description: errorMessage,
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>邮箱</FormLabel>
              <FormControl>
                <Input placeholder="<EMAIL>" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>密码</FormLabel>
              <FormControl>
                <Input type="password" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button type="submit" disabled={isLoading}>
          {isLoading ? '登录中...' : '登录'}
        </Button>
      </form>
    </Form>
  )
} 