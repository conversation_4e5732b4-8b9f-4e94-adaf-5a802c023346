// login-form.tsx 代码解释

/*
 * 表单初始化代码解释：
 * 
 * const form = useForm<z.infer<typeof formSchema>>({
 *   resolver: zodResolver(formSchema),
 *   defaultValues: {
 *     email: '',
 *     password: '',
 *   },
 * })
 * 
 * 1. useForm Hook
 *    - useForm 是 React Hook Form 的核心钩子
 *    - 用于创建和管理表单状态
 *    - 返回表单操作的方法和状态
 * 
 * 2. TypeScript 泛型 <z.infer<typeof formSchema>>
 *    - 使用 z.infer 从 Zod schema 中推断出 TypeScript 类型
 *    - 确保表单数据类型与 schema 定义匹配
 *    - 提供完整的类型检查和代码提示
 * 
 * 3. resolver 配置
 *    - resolver: zodResolver(formSchema)
 *    - 设置表单验证解析器
 *    - zodResolver 将 Zod schema 转换为表单验证规则
 *    - formSchema 定义了表单字段的验证规则（如：必填、邮箱格式等）
 * 
 * 4. defaultValues 配置
 *    - 设置表单字段的初始值
 *    - email: '' - 邮箱字段初始为空字符串
 *    - password: '' - 密码字段初始为空字符串
 * 
 * 这种设置的优点：
 * - 类型安全：TypeScript 可以检查表单数据类型
 * - 自动验证：使用 Zod 进行数据验证
 * - 集中管理：统一管理表单状态和验证规则
 * - 性能优化：React Hook Form 优化了表单重渲染
 */