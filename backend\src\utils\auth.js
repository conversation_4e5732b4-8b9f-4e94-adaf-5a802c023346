const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

/**
 * 密码加密
 * @param {string} password 原始密码
 * @returns {Promise<string>} 加密后的密码
 */
const hashPassword = async (password) => {
    const salt = await bcrypt.genSalt(10);
    return bcrypt.hash(password, salt);
};

/**
 * 密码验证
 * @param {string} password 原始密码
 * @param {string} hash 加密后的密码
 * @returns {Promise<boolean>} 验证结果
 */
const verifyPassword = async (password, hash) => {
    return bcrypt.compare(password, hash);
};

/**
 * 生成JWT令牌
 * @param {Object} payload JWT载荷
 * @returns {string} JWT令牌
 */
const generateToken = (payload) => {
    return jwt.sign(payload, process.env.JWT_SECRET, {
        expiresIn: process.env.JWT_EXPIRES_IN || '24h'
    });
};

/**
 * 验证JWT令牌
 * @param {string} token JWT令牌
 * @returns {Object} 解码后的载荷
 */
const verifyToken = (token) => {
    try {
        return jwt.verify(token, process.env.JWT_SECRET);
    } catch (error) {
        throw new Error('Invalid token');
    }
};

module.exports = {
    hashPassword,
    verifyPassword,
    generateToken,
    verifyToken
}; 