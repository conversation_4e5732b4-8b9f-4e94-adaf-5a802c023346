// 导入 Express 框架
const express = require('express');
// 导入 CORS 中间件，用于处理跨域请求1
const cors = require('cors');
// 导入 Helmet 中间件，用于增加 HTTP 头部安全1
const helmet = require('helmet');
// 导入 Morgan 中间件，用于 HTTP 请求日志记录
const morgan = require('morgan');
// 导入并配置环境变量
require('dotenv').config();

// 导入认证相关路由
const authRoutes = require('./routes/auth.routes');
// 导入笔记相关路由
const notesRoutes = require('./routes/notes.routes');
// 导入错误处理中间件
const errorMiddleware = require('./middlewares/error.middleware');

// 创建 Express 应用实例
const app = express();

// 启用 Morgan 日志中间件，使用开发环境格式
app.use(morgan('dev'));

// 配置中间件
// 启用 CORS，允许跨域请求
app.use(cors());
// 启用 Helmet，增加安全性
app.use(helmet());
// 解析 JSON 格式的请求，先检查 是否为 application /json 格式 ，然后转换，并添加 req 添加body属性
app.use(express.json());
// 解析 URL 编码的请求体，extended: true 允许解析嵌套对象
app.use(express.urlencoded({ extended: true }));

// 自定义请求日志中间件
app.use((req, res, next) => {
    // 打印请求分隔符
    console.log('\n=== 新请求 ===');
    // 记录请求时间
    console.log('时间:', new Date().toISOString());
    // 记录请求方法
    console.log('方法:', req.method);
    // 记录请求 URL
    console.log('URL:', req.url);
    // 记录请求头
    console.log('Headers:', JSON.stringify(req.headers, null, 2));
    // 如果存在请求体，则记录请求体内容
    if (req.body && Object.keys(req.body).length > 0) {
        console.log('Body:', JSON.stringify(req.body, null, 2));
    }
    // 继续处理请求
    next();
});

// 定义根路由
app.get('/', (req, res) => {
    res.json({ message: 'Welcome to Notes API' });
});

// 注册 API 路由
// 所有认证相关的路由都以 /api/auth 开头
app.use('/api/auth', authRoutes);
// 所有笔记相关的路由都以 /api/notes 开头
app.use('/api/notes', notesRoutes);

// 注册错误处理中间件
app.use(errorMiddleware);

// 处理 404 错误的中间件
app.use((req, res, next) => {
    // 记录未找到的路由
    console.log('404 - 路由未找到:', req.url);
    // 返回 404 错误响应
    res.status(404).json({
        status: 'error',
        message: '路由未找到1'
    });
    // 这里不调用 next()，因为这是最后的处理
});

// 全局错误处理中间件
app.use((err, req, res, next) => {
    // 记录错误信息
    console.error('错误处理中间件捕获到错误:', err);
    // 返回错误响应
    res.status(500).json({
        status: 'error',
        message: err.message,
        // 仅在开发环境下返回错误堆栈信息
        stack: process.env.NODE_ENV === 'development' ? err.stack : undefined
    });
});

// 设置服务器端口，默认为 4000
const PORT = process.env.PORT || 4000;

// 启动服务器
app.listen(PORT, () => {
    // 打印服务器启动信息
    console.log(`服务器运行在端口 ${PORT}`);
    // 检查关键环境变量是否已配置
    console.log('环境变量检查:');
    console.log('- SUPABASE_URL:', process.env.SUPABASE_URL ? '已设置' : '未设置');
    console.log('- SUPABASE_KEY:', process.env.SUPABASE_KEY ? '已设置' : '未设置');
});

// 导出 app 实例，用于测试或其他模块引用
module.exports = app; 