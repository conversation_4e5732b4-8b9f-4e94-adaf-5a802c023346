'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { trackEvent } from '@/lib/analytics'

// 模拟一个会导致错误的组件
function ErrorComponent() {
  throw new Error('这是一个测试错误')
  return null
}

// 模拟一个耗时操作
function heavyOperation() {
  const arr = new Array(1000000).fill(0)
  return arr.reduce((acc, cur) => acc + cur, 0)
}

export function PerformanceTest() {
  const [showError, setShowError] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const handleTestError = () => {
    trackEvent('test_error_click')
    setShowError(true)
  }

  const handleTestPerformance = async () => {
    trackEvent('test_performance_click')
    setIsLoading(true)
    
    // 模拟耗时操作
    await new Promise(resolve => {
      setTimeout(() => {
        heavyOperation()
        resolve(true)
      }, 2000)
    })
    
    setIsLoading(false)
  }

  const handleTestMemory = () => {
    trackEvent('test_memory_click')
    // 模拟内存泄漏
    const leak = new Array(10000000).fill('leak')
    console.log('内存泄漏测试:', leak.length)
  }

  return (
    <div className="space-y-4 p-4">
      <h2 className="text-2xl font-bold mb-4">性能测试</h2>
      
      <div className="space-y-2">
        <Button 
          onClick={handleTestError}
          variant="destructive"
          className="w-full"
        >
          测试错误边界
        </Button>
        {showError && <ErrorComponent />}
      </div>

      <div className="space-y-2">
        <Button 
          onClick={handleTestPerformance}
          disabled={isLoading}
          className="w-full"
        >
          {isLoading ? '处理中...' : '测试性能'}
        </Button>
      </div>

      <div className="space-y-2">
        <Button 
          onClick={handleTestMemory}
          variant="outline"
          className="w-full"
        >
          测试内存
        </Button>
      </div>
    </div>
  )
} 