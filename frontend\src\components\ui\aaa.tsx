import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Toaster } from './toaster';
import { useToast, toast as toastFn, resetToastStateForTesting } from '@/hooks/use-toast';
import { Button } from './button';
import { ToastAction } from './toast';

// 一个简单的组件，使用useToast hook来显示toasts
const ToastDemo = ({ toastProps }: { toastProps: any }) => {
  const React = require('react'); // 确保React在组件作用域内可用
  const { toast } = useToast();
  return <Button onClick={() => toast(toastProps)}>显示Toast</Button>;
};