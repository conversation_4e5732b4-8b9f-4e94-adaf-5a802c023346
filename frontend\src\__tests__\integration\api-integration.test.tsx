import { render, screen, waitFor, act } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import axios from 'axios'
import { useAuthStore } from '@/store/auth'
import { useNotesStore } from '@/store/notes'
import { api } from '@/lib/api'

// Mock axios
jest.mock('axios', () => ({
  create: jest.fn(() => ({
    post: jest.fn(),
    get: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
    defaults: { headers: { common: {} } },
    interceptors: {
      request: { use: jest.fn() },
      response: { use: jest.fn() },
    },
  })),
}))

// Mock stores
jest.mock('@/store/auth', () => ({
  useAuthStore: {
    getState: jest.fn(),
    setState: jest.fn(),
  },
}))

jest.mock('@/store/notes', () => ({
  useNotesStore: {
    getState: jest.fn(),
    setState: jest.fn(),
  },
}))

const mockAxios = axios.create() as jest.Mocked<any>
const mockAuthStore = useAuthStore as jest.Mocked<any>
const mockNotesStore = useNotesStore as jest.Mocked<any>

// Mock API responses
const mockAuthResponse = {
  data: {
    status: 'success',
    data: {
      user: { id: '1', email: '<EMAIL>', name: 'Test User' },
      token: 'mock-jwt-token'
    }
  }
}

const mockNotesResponse = {
  data: {
    status: 'success',
    data: [
      {
        id: '1',
        title: 'Test Note',
        content: 'Test content',
        tags: ['test'],
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        user_id: '1'
      }
    ]
  }
}

const mockNoteResponse = {
  data: {
    status: 'success',
    data: {
      id: '1',
      title: 'Test Note',
      content: 'Test content',
      tags: ['test'],
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      user_id: '1'
    }
  }
}

// Test wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe('API集成测试', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    
    // Setup axios mock
    ;(axios.create as jest.Mock).mockReturnValue(mockAxios)
    
    // Setup store mocks
    mockAuthStore.getState.mockReturnValue({
      user: null,
      token: null,
      isAuthenticated: false,
    })
    
    mockNotesStore.getState.mockReturnValue({
      selectedNote: null,
      searchQuery: '',
      selectedTags: [],
    })
  })

  describe('认证API集成', () => {
    describe('登录', () => {
      it('应该成功登录并返回用户数据', async () => {
        mockAxios.post.mockResolvedValue(mockAuthResponse)

        const result = await api.auth.login('<EMAIL>', 'password123')

        expect(mockAxios.post).toHaveBeenCalledWith('/auth/login', {
          email: '<EMAIL>',
          password: 'password123'
        })

        expect(result.data).toEqual(mockAuthResponse.data)
      })

      it('应该处理登录失败', async () => {
        const errorResponse = {
          response: {
            status: 401,
            data: {
              status: 'error',
              message: '邮箱或密码错误'
            }
          }
        }

        mockAxios.post.mockRejectedValue(errorResponse)

        try {
          await api.auth.login('<EMAIL>', 'wrongpassword')
        } catch (error: any) {
          expect(error.response.status).toBe(401)
          expect(error.response.data.message).toBe('邮箱或密码错误')
        }
      })
    })

    describe('注册', () => {
      it('应该成功注册新用户', async () => {
        mockAxios.post.mockResolvedValue(mockAuthResponse)

        const result = await api.auth.register('<EMAIL>', 'password123', 'Test User')

        expect(mockAxios.post).toHaveBeenCalledWith('/auth/register', {
          email: '<EMAIL>',
          password: 'password123',
          name: 'Test User'
        })

        expect(result.data.data.user.email).toBe('<EMAIL>')
      })

      it('应该处理邮箱已存在的错误', async () => {
        const errorResponse = {
          response: {
            status: 409,
            data: {
              status: 'error',
              message: '邮箱已被注册'
            }
          }
        }

        mockAxios.post.mockRejectedValue(errorResponse)

        try {
          await api.auth.register('<EMAIL>', 'password123', 'Test User')
        } catch (error: any) {
          expect(error.response.status).toBe(409)
          expect(error.response.data.message).toBe('邮箱已被注册')
        }
      })
    })

    describe('获取用户信息', () => {
      it('应该能够获取当前用户信息', async () => {
        const userResponse = {
          data: { id: '1', email: '<EMAIL>', name: 'Test User' }
        }

        mockAxios.get.mockResolvedValue(userResponse)

        const result = await api.auth.getProfile()

        expect(mockAxios.get).toHaveBeenCalledWith('/auth/me')
        expect(result.data.email).toBe('<EMAIL>')
      })
    })
  })

  describe('笔记API集成', () => {
    beforeEach(() => {
      // 设置已认证状态
      Object.defineProperty(window, 'localStorage', {
        value: {
          getItem: jest.fn(() => 'mock-jwt-token'),
          setItem: jest.fn(),
          removeItem: jest.fn(),
        },
        writable: true,
      })
    })

    describe('获取笔记列表', () => {
      it('应该成功获取笔记列表', async () => {
        mockAxios.get.mockResolvedValue(mockNotesResponse)

        const result = await api.notes.getAll()

        expect(mockAxios.get).toHaveBeenCalledWith('/notes', { params: {} })
        expect(result.data).toHaveLength(1)
        expect(result.data[0].title).toBe('Test Note')
      })

      it('应该支持分页参数', async () => {
        mockAxios.get.mockResolvedValue(mockNotesResponse)

        await api.notes.getAll({ page: 1, limit: 10 })

        expect(mockAxios.get).toHaveBeenCalledWith('/notes', {
          params: { page: 1, limit: 10 }
        })
      })

      it('应该支持搜索参数', async () => {
        mockAxios.get.mockResolvedValue(mockNotesResponse)

        await api.notes.getAll({ search: 'test', tag: 'work' })

        expect(mockAxios.get).toHaveBeenCalledWith('/notes', {
          params: { search: 'test', tag: 'work' }
        })
      })
    })

    describe('创建笔记', () => {
      it('应该成功创建新笔记', async () => {
        mockAxios.post.mockResolvedValue(mockNoteResponse)

        const noteData = {
          title: 'New Note',
          content: 'New content',
          tags: ['new', 'test'],
          user_id: '1'
        }

        const result = await api.notes.create(noteData)

        expect(mockAxios.post).toHaveBeenCalledWith('/notes', noteData)
        expect(result.title).toBe('Test Note')
      })

      it('应该处理创建笔记时的验证错误', async () => {
        const errorResponse = {
          response: {
            status: 400,
            data: {
              status: 'error',
              message: '标题不能为空',
              errors: {
                title: ['标题是必填项']
              }
            }
          }
        }

        mockAxios.post.mockRejectedValue(errorResponse)

        try {
          await api.notes.create({
            title: '',
            content: 'Content',
            tags: [],
            user_id: '1'
          })
        } catch (error: any) {
          expect(error.response.status).toBe(400)
          expect(error.response.data.errors.title).toContain('标题是必填项')
        }
      })
    })

    describe('更新笔记', () => {
      it('应该成功更新笔记', async () => {
        const updatedNoteResponse = {
          data: {
            ...mockNoteResponse.data,
            title: 'Updated Note',
            content: 'Updated content'
          }
        }

        mockAxios.put.mockResolvedValue(updatedNoteResponse)

        const updateData = {
          title: 'Updated Note',
          content: 'Updated content',
          tags: ['updated']
        }

        const result = await api.notes.update('1', updateData)

        expect(mockAxios.put).toHaveBeenCalledWith('/notes/1', updateData)
        expect(result.title).toBe('Updated Note')
      })

      it('应该处理更新不存在的笔记', async () => {
        const errorResponse = {
          response: {
            status: 404,
            data: {
              status: 'error',
              message: '笔记不存在'
            }
          }
        }

        mockAxios.put.mockRejectedValue(errorResponse)

        try {
          await api.notes.update('999', {
            title: 'Updated Note',
            content: 'Updated content',
            tags: []
          })
        } catch (error: any) {
          expect(error.response.status).toBe(404)
          expect(error.response.data.message).toBe('笔记不存在')
        }
      })
    })

    describe('删除笔记', () => {
      it('应该成功删除笔记', async () => {
        mockAxios.delete.mockResolvedValue({ data: { success: true } })

        await api.notes.delete('1')

        expect(mockAxios.delete).toHaveBeenCalledWith('/notes/1')
      })

      it('应该处理删除不存在的笔记', async () => {
        const errorResponse = {
          response: {
            status: 404,
            data: {
              status: 'error',
              message: '笔记不存在'
            }
          }
        }

        mockAxios.delete.mockRejectedValue(errorResponse)

        try {
          await api.notes.delete('999')
        } catch (error: any) {
          expect(error.response.status).toBe(404)
          expect(error.response.data.message).toBe('笔记不存在')
        }
      })
    })

    describe('获取标签', () => {
      it('应该成功获取所有标签', async () => {
        const tagsResponse = {
          data: ['工作', '学习', '重要']
        }

        mockAxios.get.mockResolvedValue(tagsResponse)

        const result = await api.notes.getTags()

        expect(mockAxios.get).toHaveBeenCalledWith('/notes/tags')
        expect(result).toEqual(['工作', '学习', '重要'])
      })
    })
  })

  describe('网络错误处理', () => {
    it('应该处理网络连接失败', async () => {
      const networkError = new Error('Network Error')
      networkError.name = 'NetworkError'

      mockAxios.get.mockRejectedValue(networkError)

      try {
        await api.notes.getAll()
      } catch (error: any) {
        expect(error.message).toBe('Network Error')
      }
    })

    it('应该处理超时错误', async () => {
      const timeoutError = new Error('timeout of 5000ms exceeded')
      timeoutError.name = 'TimeoutError'

      mockAxios.get.mockRejectedValue(timeoutError)

      try {
        await api.notes.getAll()
      } catch (error: any) {
        expect(error.message).toContain('timeout')
      }
    })

    it('应该处理服务器内部错误', async () => {
      const serverError = {
        response: {
          status: 500,
          data: {
            status: 'error',
            message: '服务器内部错误'
          }
        }
      }

      mockAxios.get.mockRejectedValue(serverError)

      try {
        await api.notes.getAll()
      } catch (error: any) {
        expect(error.response.status).toBe(500)
        expect(error.response.data.message).toBe('服务器内部错误')
      }
    })
  })
}) 