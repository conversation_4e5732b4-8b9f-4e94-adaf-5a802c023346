'use client'

import { redirect } from 'next/navigation'
import { getToken } from '@/lib/cookies'
import { Header } from '@/components/layout/header'

export default function ProtectedLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const token = getToken()

  if (!token) {
    redirect('/login')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      {/* 背景装饰元素 */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-600/20 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-green-400/20 to-blue-600/20 rounded-full blur-3xl"></div>
      </div>
      
      {/* Header */}
      <Header />
      
      {/* 主要内容区域 */}
      <div className="relative z-10">
        <div className="container mx-auto px-4 py-6 max-w-7xl">
          {children}
        </div>
      </div>
    </div>
  )
} 