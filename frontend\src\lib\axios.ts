import axios from 'axios'
import { getToken, getRefreshToken, setToken, removeToken, removeRefreshToken } from './cookies'

const baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/api'

export const axiosInstance = axios.create({
  baseURL,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
axiosInstance.interceptors.request.use(
  (config) => {
    // 从 cookies 获取 token
    const token = getToken()
    
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
axiosInstance.interceptors.response.use(
  (response) => response.data,
  async (error) => {
    const originalRequest = error.config

    // 如果是 401 错误且不是重试请求
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true

      try {
        // 尝试刷新 token
        const refreshToken = getRefreshToken()
        if (refreshToken) {
          console.log('🔄 Access Token 过期，尝试使用 Refresh Token 刷新...')
          
          const response = await axios.post(`${baseURL}/auth/refresh`, {
            refreshToken,
          })
          
          const { token } = response.data
          setToken(token) // 使用cookie存储
          
          console.log('✅ Token 刷新成功，继续原始请求')
          
          // 更新原始请求的 token
          originalRequest.headers.Authorization = `Bearer ${token}`
          return axiosInstance(originalRequest)
        } else {
          console.log('❌ 没有找到 Refresh Token，跳转登录')
          throw new Error('No refresh token available')
        }
      } catch (refreshError: any) {
        // 刷新 token 失败的详细处理
        console.log('💥 Refresh Token 刷新失败:', refreshError)
        
        // 清除所有认证信息
        removeToken()
        removeRefreshToken()
        
        if (typeof window !== 'undefined') {
          // 在浏览器环境中处理跳转
          const currentPath = window.location.pathname
          
          // 显示友好的错误信息
          if (refreshError.response?.status === 401) {
            console.log('🔒 Refresh Token 已过期，需要重新登录')
            // 可以显示toast提示
            if (window.dispatchEvent) {
              window.dispatchEvent(new CustomEvent('auth:expired', {
                detail: { message: '登录已过期，请重新登录' }
              }))
            }
          } else {
            console.log('🚫 Token 刷新遇到其他错误')
            if (window.dispatchEvent) {
              window.dispatchEvent(new CustomEvent('auth:error', {
                detail: { message: '认证失败，请重新登录' }
              }))
            }
          }
          
          // 避免在登录页面重复跳转
          if (currentPath !== '/login' && currentPath !== '/register') {
            // 保存当前页面路径，登录后可以跳转回来
            sessionStorage.setItem('redirectAfterLogin', currentPath)
            window.location.href = '/login'
          }
        }
        
        // 继续抛出错误，让调用方知道请求失败了
        return Promise.reject(new Error('Authentication failed'))
      }
    }

    return Promise.reject(error)
  }
) 