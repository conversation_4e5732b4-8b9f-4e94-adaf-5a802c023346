import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Toaster } from './toaster';
import { useToast, toast as toastFn, resetToastStateForTesting } from '@/hooks/use-toast';
import { Button } from './button';
import { ToastAction } from './toast';

// 一个简单的组件，使用useToast hook来显示toasts
const ToastDemo = ({ toastProps }: { toastProps: any }) => {
  const React = require('react'); // 确保React在组件作用域内可用
  const { toast } = useToast();
  return <Button onClick={() => toast(toastProps)}>显示Toast</Button>;
};

// 模拟 Radix UI Primitives 以避免复杂的Portal和动画交互
// 对于Toast, 我们主要关心的是Provider, Viewport和Root的行为
jest.mock('@radix-ui/react-toast', () => {
  const React = require('react'); // 在jest.mock顶层require React
  const actual = jest.requireActual('@radix-ui/react-toast');
  
  const MockedProvider = ({ children }: { children: React.ReactNode }) => <div>{children}</div>;
  MockedProvider.displayName = 'MockedToastProvider';

  const MockedViewport = ({ children, ...props }: { children: React.ReactNode, [key: string]: any }) => (
    <div {...props} data-testid="toast-viewport">
      {children}
    </div>
  );
  MockedViewport.displayName = 'MockedToastViewport';

  const MockedClose = React.forwardRef<HTMLButtonElement, { onClick?: () => void, 'aria-label'?: string, [key: string]: any }>((props, ref) => {
    const { onClick, 'aria-label': ariaLabel, onOpenChange, ...rest } = props; 
    // onOpenChange is extracted and not passed to button if it caused warnings
    return (
        <button 
            ref={ref} 
            aria-label={ariaLabel || "关闭Toast"} // Ensure aria-label is always present
            onClick={onClick} // This should be the dismiss function
            {...rest} // Spread remaining props
        >
        </button>
    );
  });
  MockedClose.displayName = actual.Close.displayName;  

  const MockedRoot = React.forwardRef<
    HTMLDivElement, 
    { 
      children: React.ReactNode, 
      open?: boolean, 
      className?: string, // Expect className instead of variant
      onOpenChange?: (open: boolean) => void, 
      id?: string, 
      [key: string]: any 
    }
  >((allProps, ref) => {
      const { children, open, className, onOpenChange, id, ...restRenderProps } = allProps;
      
      if (!open) {
        return null;
      }
      
      // Simulate onOpenChange being called by MockedClose
      // Children are mapped to pass down onOpenChange to a potential MockedClose child
      const childrenWithProps = React.Children.map(children, (child: any) => {
        if (React.isValidElement(child)) {
          // Check if this child is a close button (either by type or displayName)
          const isCloseButton = child.type === MockedClose || 
                               child.type?.displayName?.includes('Close') ||
                               child.type?.name?.includes('Close');
          
          if (isCloseButton) {
            return React.cloneElement(child, { 
              onClick: () => {
                onOpenChange?.(false);
              }
            });
          }
        }
        return child;
      });

      return (
        <div 
          ref={ref} 
          role="status" 
          className={className} // Apply the className from props
          data-testid={`toast-root-${id || 'test'}`} 
          data-state={open ? 'open' : 'closed'}
          {...restRenderProps} // Spread only the remaining props (ensure onOpenChange is not here if it warns)
        >
          {childrenWithProps}
        </div>
      );
    }
  );
  MockedRoot.displayName = actual.Root.displayName;

  const MockedTitle = ({children, ...props}: { children: React.ReactNode, [key: string]: any }) => <div {...props} className="toast-title">{children}</div>;
  MockedTitle.displayName = actual.Title.displayName;

  const MockedDescription = ({children, ...props}: { children: React.ReactNode, [key: string]: any }) => <div {...props} className="toast-description">{children}</div>;
  MockedDescription.displayName = actual.Description.displayName;

  const MockedAction = React.forwardRef<HTMLButtonElement, { children: React.ReactNode, altText?: string, [key: string]: any }>(({ children, altText, ...props }, ref) => (
    <button ref={ref} {...props} aria-label={altText || 'Toast Action'}>
      {children}
    </button>
  ));
  MockedAction.displayName = actual.Action.displayName;

  return {
    ...actual,
    Provider: MockedProvider,
    Viewport: MockedViewport,
    Root: MockedRoot,
    Close: MockedClose,
    Title: MockedTitle,
    Description: MockedDescription,
    Action: MockedAction, 
  };
});


describe('Toaster 和 useToast 集成测试', () => {
  beforeEach(() => {
    resetToastStateForTesting();
    jest.clearAllMocks();
  });

  it('应该使用 useToast hook 正确显示默认Toast', async () => {
    const user = userEvent.setup();
    render(
      <>
        <Toaster />
        <ToastDemo toastProps={{ title: '默认标题', description: '默认描述' }} />
      </>
    );
    await user.click(screen.getByText('显示Toast'));
    const titleElement = await screen.findByText('默认标题', { selector: '.toast-title' });
    expect(titleElement).toBeInTheDocument();
    const toastElement = await screen.findByTestId('toast-root-1');
    expect(toastElement).toHaveAttribute('data-state', 'open');
    // Default variant should not have 'destructive' class
    expect(toastElement).not.toHaveClass('destructive'); 
  });

  it('应该使用 useToast hook 正确显示 destructive Toast', async () => {
    const user = userEvent.setup();
    render(
      <>
        <Toaster />
        <ToastDemo
          toastProps={{
            variant: 'destructive',
            title: '错误标题',
            description: '错误描述',
          }}
        />
      </>
    );
    await user.click(screen.getByText('显示Toast'));
    const titleElement = await screen.findByText('错误标题', { selector: '.toast-title' });
    expect(titleElement).toBeInTheDocument();
    const toastElement = await screen.findByTestId('toast-root-1');
    // Check for the class associated with the destructive variant
    expect(toastElement).toHaveClass('destructive'); 
    expect(toastElement).toHaveAttribute('data-state', 'open');
  });

  it('点击关闭按钮时应该关闭Toast（通过useToast）', async () => {
    const user = userEvent.setup();
    render(
      <>
        <Toaster />
        <ToastDemo toastProps={{ title: '待关闭', description: '描述' }} />
      </>
    );
    await user.click(screen.getByText('显示Toast'));
    const titleElement = await screen.findByText('待关闭', { selector: '.toast-title' });
    expect(titleElement).toBeInTheDocument();
    
    const toastElement = await screen.findByTestId('toast-root-1');
    const closeButton = within(toastElement).getByRole('button', { name: '关闭Toast' });
    await user.click(closeButton);
    
    await waitFor(() => {
      expect(screen.queryByTestId('toast-root-1')).not.toBeInTheDocument();
    });
  });

  it('应该正确渲染带操作按钮的Toast（通过useToast）', async () => {
    const user = userEvent.setup();
    const handleActionClick = jest.fn();
    render(
      <>
        <Toaster />
        <Button
          onClick={() =>
            toastFn({
              title: '操作提醒',
              description: '请点击操作按钮',
              action: (
                <ToastAction altText="执行操作按钮" onClick={handleActionClick}>
                  执行
                </ToastAction>
              ),
            })
          }
        >
          显示操作Toast
        </Button>
      </>
    );
    await user.click(screen.getByText('显示操作Toast'));
    expect(await screen.findByText('操作提醒', { selector: '.toast-title' })).toBeInTheDocument();
    const toastElement = await screen.findByTestId('toast-root-1');
    const actionButton = within(toastElement).getByRole('button', { name: '执行操作按钮' });
    expect(actionButton).toBeInTheDocument();
    expect(actionButton).toHaveTextContent('执行');

    await user.click(actionButton);
    expect(handleActionClick).toHaveBeenCalledTimes(1);
  });

  it('ToastViewport 应该在Toaster中正确渲染', () => {
    render(<Toaster />);
    expect(screen.getByTestId('toast-viewport')).toBeInTheDocument();
  });
}); 