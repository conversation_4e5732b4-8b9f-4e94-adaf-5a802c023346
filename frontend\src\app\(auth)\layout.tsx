'use client'

import { redirect } from 'next/navigation'
import { getToken } from '@/lib/cookies'

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const token = getToken()

  if (token) {
    redirect('/notes')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100 dark:from-slate-900 dark:via-blue-900 dark:to-indigo-900 relative overflow-hidden">
      {/* 背景装饰元素 */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* 主要装饰圆圈 */}
        <div className="absolute top-1/4 -left-20 w-96 h-96 bg-gradient-to-br from-blue-400/30 to-purple-600/30 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 -right-20 w-96 h-96 bg-gradient-to-br from-purple-400/30 to-pink-600/30 rounded-full blur-3xl animate-pulse"></div>
        
        {/* 小装饰元素 */}
        <div className="absolute top-1/3 right-1/4 w-32 h-32 bg-gradient-to-br from-yellow-400/20 to-orange-600/20 rounded-full blur-2xl"></div>
        <div className="absolute bottom-1/3 left-1/4 w-24 h-24 bg-gradient-to-br from-green-400/20 to-blue-600/20 rounded-full blur-2xl"></div>
        
        {/* 网格图案 */}
        <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:50px_50px]"></div>
      </div>
      
      {/* 主要内容 */}
      <div className="relative z-10 flex min-h-screen items-center justify-center p-4">
        <div className="w-full max-w-md">
          {/* 卡片容器 */}
          <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 dark:border-gray-700/50 p-8">
            {children}
          </div>
          
          {/* 底部装饰文字 */}
          <p className="text-center text-sm text-gray-500 dark:text-gray-400 mt-6">
            © 2024 Notes App. 让想法不再丢失 ✨
          </p>
        </div>
      </div>
    </div>
  )
} 