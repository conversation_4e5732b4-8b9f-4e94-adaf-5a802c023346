import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
} from './form';
import { Input } from './input';

// 测试用的表单验证 Schema
const formSchema = z.object({
  username: z.string().min(2, '用户名至少2个字符'),
  email: z.string().email('请输入有效的邮箱地址'),
});

// 测试用的表单组件
const TestForm = ({ onSubmit = () => {} }) => {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      username: '',
      email: '',
    },
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <FormField
          control={form.control}
          name="username"
          render={({ field }) => (
            <FormItem>
              <FormLabel>用户名</FormLabel>
              <FormControl>
                <Input placeholder="请输入用户名" {...field} />
              </FormControl>
              <FormDescription>
                这是你的用户名
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>邮箱</FormLabel>
              <FormControl>
                <Input placeholder="请输入邮箱" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <button type="submit">提交</button>
      </form>
    </Form>
  );
};

describe('Form 组件', () => {
  it('应该正确渲染表单字段', () => {
    render(<TestForm />);
    
    expect(screen.getByLabelText('用户名')).toBeInTheDocument();
    expect(screen.getByLabelText('邮箱')).toBeInTheDocument();
    expect(screen.getByText('这是你的用户名')).toBeInTheDocument();
  });

  it('应该显示验证错误信息', async () => {
    render(<TestForm />);
    
    // 提交空表单触发验证
    fireEvent.click(screen.getByText('提交'));
    
    await waitFor(() => {
      expect(screen.getByText('用户名至少2个字符')).toBeInTheDocument();
      expect(screen.getByText('请输入有效的邮箱地址')).toBeInTheDocument();
    });
  });

  it('应该正确处理表单提交', async () => {
    const handleSubmit = jest.fn();
    render(<TestForm onSubmit={handleSubmit} />);
    
    // 填写有效数据
    fireEvent.change(screen.getByLabelText('用户名'), {
      target: { value: 'testuser' },
    });
    fireEvent.change(screen.getByLabelText('邮箱'), {
      target: { value: '<EMAIL>' },
    });
    
    // 提交表单
    fireEvent.click(screen.getByText('提交'));
    
    await waitFor(() => {
      expect(handleSubmit).toHaveBeenCalledWith(
        {
          username: 'testuser',
          email: '<EMAIL>',
        },
        expect.anything()
      );
    });
  });

  it('应该正确处理表单控件的禁用状态', () => {
    const TestDisabledForm = () => {
      const form = useForm();
      return (
        <Form {...form}>
          <form>
            <FormField
              control={form.control}
              name="test"
              render={() => (
                <FormItem>
                  <FormControl>
                    <Input disabled />
                  </FormControl>
                </FormItem>
              )}
            />
          </form>
        </Form>
      );
    };
    
    render(<TestDisabledForm />);
    expect(screen.getByRole('textbox')).toBeDisabled();
  });

  it('应该正确显示错误状态的样式', async () => {
    render(<TestForm />);
    
    // 输入无效的邮箱地址
    fireEvent.change(screen.getByLabelText('邮箱'), {
      target: { value: 'invalid-email' },
    });
    
    // 提交表单触发验证
    fireEvent.click(screen.getByText('提交'));
    
    await waitFor(() => {
      const errorMessage = screen.getByText('请输入有效的邮箱地址');
      expect(errorMessage).toHaveClass('text-destructive');
    });
  });

  it('应该正确处理表单重置', async () => {
    const TestFormWithReset = () => {
      const form = useForm({
        defaultValues: {
          username: '',
          email: '',
        },
      });
    
      return (
        <Form {...form}>
          <form>
            <FormField
              control={form.control}
              name="username"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
            <button type="button" onClick={() => form.reset()}>
              重置
            </button>
          </form>
        </Form>
      );
    };
    
    render(<TestFormWithReset />);
    
    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: 'test' } });
    expect(input).toHaveValue('test');
    
    fireEvent.click(screen.getByText('重置'));
    expect(input).toHaveValue('');
  });
}); 