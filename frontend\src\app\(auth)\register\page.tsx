import Link from 'next/link'
import { RegisterForm } from '@/components/auth/register-form'
import { UserPlus, Sparkles, ArrowRight } from 'lucide-react'

export default function RegisterPage() {
  return (
    <div className="space-y-8">
      {/* 页面标题区域 */}
      <div className="text-center space-y-4">
        {/* 图标 */}
        <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl shadow-lg mb-4">
          <UserPlus className="w-8 h-8 text-white" />
        </div>
        
        {/* 标题和描述 */}
        <div className="space-y-2">
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">
            加入我们
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            开始你的笔记之旅，记录每一个精彩想法
          </p>
        </div>
        
        {/* 装饰元素 */}
        <div className="flex items-center justify-center space-x-2 text-yellow-500">
          <Sparkles className="w-4 h-4 animate-pulse" />
          <span className="text-sm text-gray-500 dark:text-gray-400">免费注册，即刻开始</span>
          <Sparkles className="w-4 h-4 animate-pulse" />
        </div>
      </div>

      {/* 注册表单 */}
      <RegisterForm />
      
      {/* 登录链接 */}
      <div className="text-center">
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-200 dark:border-gray-700"></div>
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-white dark:bg-gray-900 px-2 text-gray-500 dark:text-gray-400">
              或者
            </span>
          </div>
        </div>
        
        <div className="mt-6">
          <Link 
            href="/login" 
            className="group inline-flex items-center gap-2 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium transition-colors duration-200"
          >
            <span>已有账号？立即登录</span>
            <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-200" />
          </Link>
        </div>
      </div>
    </div>
  )
} 