'use client' // 第 1 行: Next.js 指令，将此文件标记为客户端组件。因为此布局涉及到客户端逻辑（如重定向和读取 cookie），所以需要是客户端组件。

// 第 2 行: 空行，用于可读性。
import { redirect } from 'next/navigation' // 第 3 行: 从 'next/navigation' 导入 redirect 函数，用于在客户端进行路由重定向。
import { getToken } from '@/lib/cookies' // 第 4 行: 从项目路径 '@/lib/cookies' 导入 getToken 函数，可能用于从 cookie 中获取用户认证 token。

// 第 5 行: 空行。
export default function AuthLayout({ // 第 6 行: 定义并默认导出名为 AuthLayout 的函数组件。这是 '(auth)' 路由组的布局组件。
  children, // 第 7 行: 解构 props，获取 children，它代表被此布局包裹的子组件或子页面（例如登录页、注册页）。
}: { // 第 8 行: 开始定义 props 对象的 TypeScript 类型注解。
  children: React.ReactNode // 第 9 行: 定义 children prop 的类型为 React.ReactNode，表示它可以是任何有效的 React 子节点。
}) { // 第 10 行: AuthLayout 函数组件参数列表和类型注解的结束括号，以及函数体的开始花括号。
  const token = getToken() // 第 11 行: 调用 getToken 函数获取用户当前的认证 token，并将其存储在 token 常量中。

  // 第 12 行: 空行。
  if (token) { // 第 13 行: 条件语句，检查 token 是否存在（即用户是否已登录）。
    redirect('/dashboard') // 第 14 行: 如果 token 存在 (用户已登录)，则调用 redirect 函数将用户重定向到 '/dashboard' 路径。
  } // 第 15 行: if 语句块的结束。

  // 第 16 行: 空行。
  return ( // 第 17 行: 组件的 return 语句，定义了当用户未登录时，认证相关页面的布局结构。
    <div className="container flex h-screen w-screen flex-col items-center justify-center"> {/* 第 18 行: 最外层 div 容器。应用 Tailwind CSS 类：'container' (可能用于最大宽度和居中)，'flex' (弹性布局)，'h-screen' (高度占满屏幕)，'w-screen' (宽度占满屏幕)，'flex-col' (子元素垂直排列)，'items-center' (交叉轴居中)，'justify-center' (主轴居中)。这会使内部内容在屏幕上垂直和水平居中。 */}
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]"> {/* 第 19 行: 内部 div 容器，用于包裹实际的表单内容。应用 Tailwind CSS 类：'mx-auto' (水平居中)，'flex'，'w-full' (宽度占满父容器)，'flex-col'，'justify-center'，'space-y-6' (子元素垂直间距为6单位)，'sm:w-[350px]' (在小屏幕及以上，宽度固定为350px)。 */}
        {children} {/* 第 20 行: 渲染传递给 AuthLayout 的子组件或子页面 (例如登录表单或注册表单)。 */}
      </div> {/* 第 21 行: 内部 div 容器的结束标签。 */}
    </div> // 第 22 行: 最外层 div 容器的结束标签。
  ) // 第 23 行: return 语句的结束括号。
} // 第 24 行: AuthLayout 函数组件定义的结束花括号。 