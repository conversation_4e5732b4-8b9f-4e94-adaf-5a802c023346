// 第 1 行: 指令，表明这个文件及其导出的模块应该被视为客户端组件。UI 组件通常是客户端组件，因为它们需要处理用户交互和浏览器事件。
'use client'

// 第 3 行: 导入整个 React 库，并将其命名为 React。
import * as React from 'react'
// 第 4 行: 导入 Radix UI Toast 库的所有导出，并将其命名为 ToastPrimitives。Radix UI 提供了无样式、功能完备的 UI 构建块。
import * as ToastPrimitives from '@radix-ui/react-toast'
// 第 5 行: 从 class-variance-authority 库导入 cva 函数和 VariantProps 类型。cva 用于创建具有可配置变体（variants）的样式。
import { cva, type VariantProps } from 'class-variance-authority'
// 第 6 行: 从 lucide-react 库导入 X 图标组件，通常用作关闭按钮的图标。
import { X } from 'lucide-react'

// 第 8 行: 从项目内部的工具库导入 cn 函数，这是一个用于合并和条件应用 Tailwind CSS 类名的辅助函数。
import { cn } from '@/lib/utils'

// 第 10 行: ToastProvider 组件，直接使用了 Radix UI Toast 的 Provider。它为 Toast 系统提供上下文，应包裹应用的根部或相关部分。
const ToastProvider = ToastPrimitives.Provider

// 第 12 行: ToastViewport 组件，用于定义 toast 通知出现的区域（视口）。
const ToastViewport = React.forwardRef< // 第 12 行: 使用 React.forwardRef 允许父组件传递 ref。
  React.ElementRef<typeof ToastPrimitives.Viewport>, // 第 13 行: ref 的类型是 Radix Viewport 元素的引用。
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Viewport> // 第 14 行: props 的类型是 Radix Viewport 组件的属性，但不包括 ref。
>(({ className, ...props }, ref) => ( // 第 15 行: 组件实现，解构 className 和其他 props。
  <ToastPrimitives.Viewport // 第 16 行: 渲染 Radix UI 的 Viewport 组件。
    ref={ref} // 第 17 行: 传递 ref。
    className={cn( // 第 18 行: 使用 cn 函数合并 Tailwind CSS 类名。
      'fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]', // 第 19 行: 基础样式：固定定位，高 z-index，flex 布局，padding，响应式位置调整（小屏幕顶部，大屏幕右下角）。
      className // 第 20 行: 合并外部传入的 className。
    )}
    {...props} // 第 22 行: 传递其他所有 props。
  />
))
ToastViewport.displayName = ToastPrimitives.Viewport.displayName // 第 25 行: 设置组件的 displayName，便于调试。

// 第 27 行: 使用 cva 定义 toast 的样式变体。
const toastVariants = cva(
  // 第 28 行: 基础类名，应用于所有变体。包括：交互、定位、布局、边框、圆角、阴影、过渡效果和 Radix UI 数据属性驱动的动画。
  'group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full',
  {
    variants: { // 第 30 行: 定义不同的变体。
      variant: { // 第 31 行: 'variant' 属性用于区分不同的 toast 类型。
        default: 'border bg-background text-foreground', // 第 32 行: 'default' 变体的样式：标准边框、背景和前景文字颜色。
        destructive: // 第 33 行: 'destructive' 变体的样式，通常用于错误或警告。
          'destructive group border-destructive bg-destructive text-destructive-foreground', // 第 34 行: 特定的边框、背景和前景颜色，并添加 'destructive' 类名供其他子组件样式化。
      },
    },
    defaultVariants: { // 第 37 行: 定义默认变体。
      variant: 'default', // 第 38 行: 如果不指定 variant prop，则默认为 'default'。
    },
  }
)

// 第 43 行: Toast 组件，代表单个 toast 通知的根元素。
const Toast = React.forwardRef< // 第 43 行: 使用 React.forwardRef。
  React.ElementRef<typeof ToastPrimitives.Root>, // 第 44 行: ref 的类型是 Radix Root 元素的引用。
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Root> & // 第 45 行: props 的类型是 Radix Root 组件的属性。
    VariantProps<typeof toastVariants> // 第 46 行: 并合并了 cva 定义的变体属性 (即 'variant' prop)。
>(({ className, variant, ...props }, ref) => { // 第 47 行: 组件实现，解构 className, variant 和其他 props。
  return (
    <ToastPrimitives.Root // 第 49 行: 渲染 Radix UI 的 Root 组件。
      ref={ref} // 第 50 行: 传递 ref。
      className={cn(toastVariants({ variant }), className)} // 第 51 行: 使用 cn 函数合并样式：首先应用 cva 根据 variant 生成的类名，然后合并外部传入的 className。
      {...props} // 第 52 行: 传递其他所有 props。
    />
  )
})
Toast.displayName = ToastPrimitives.Root.displayName // 第 56 行: 设置 displayName。

// 第 58 行: ToastAction 组件，用于在 toast 中放置一个可交互的动作按钮。
const ToastAction = React.forwardRef< // 第 58 行: 使用 React.forwardRef。
  React.ElementRef<typeof ToastPrimitives.Action>, // 第 59 行: ref 类型。
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Action> // 第 60 行: props 类型。
>(({ className, ...props }, ref) => ( // 第 61 行: 组件实现。
  <ToastPrimitives.Action // 第 62 行: 渲染 Radix UI 的 Action 组件。
    ref={ref} // 第 63 行: 传递 ref。
    className={cn( // 第 64 行: 合并样式。
      'inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive', // 第 65 行: 按钮的基本样式、过渡、焦点状态、禁用状态，以及针对父级 Toast 有 'destructive' 类名时的特定样式。
      className // 第 66 行: 合并外部 className。
    )}
    {...props} // 第 68 行: 传递其他 props。
  />
))
ToastAction.displayName = ToastPrimitives.Action.displayName // 第 71 行: 设置 displayName。

// 第 73 行: ToastClose 组件，用于渲染 toast 的关闭按钮。
const ToastClose = React.forwardRef< // 第 73 行: 使用 React.forwardRef。
  React.ElementRef<typeof ToastPrimitives.Close>, // 第 74 行: ref 类型。
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Close> // 第 75 行: props 类型。
>(({ className, ...props }, ref) => ( // 第 76 行: 组件实现。
  <ToastPrimitives.Close // 第 77 行: 渲染 Radix UI 的 Close 组件。
    ref={ref} // 第 78 行: 传递 ref。
    className={cn( // 第 79 行: 合并样式。
      'absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600', // 第 80 行: 关闭按钮的绝对定位、样式、过渡、焦点状态，以及父级有 'destructive' 或被 hover 时的特定样式 (初始透明，hover 或 focus 时可见)。
      className // 第 81 行: 合并外部 className。
    )}
    toast-close="" // 第 83 行: 一个空属性，可能用于 CSS 选择器或 Radix UI 内部处理。
    {...props} // 第 84 行: 传递其他 props。
  >
    <X className="h-4 w-4" /> {/* 第 86 行: 在关闭按钮内部渲染 X 图标。 */}
  </ToastPrimitives.Close>
))
ToastClose.displayName = ToastPrimitives.Close.displayName // 第 89 行: 设置 displayName。

// 第 91 行: ToastTitle 组件，用于显示 toast 的标题。
const ToastTitle = React.forwardRef< // 第 91 行: 使用 React.forwardRef。
  React.ElementRef<typeof ToastPrimitives.Title>, // 第 92 行: ref 类型。
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Title> // 第 93 行: props 类型。
>(({ className, ...props }, ref) => ( // 第 94 行: 组件实现。
  <ToastPrimitives.Title // 第 95 行: 渲染 Radix UI 的 Title 组件。
    ref={ref} // 第 96 行: 传递 ref。
    className={cn('text-sm font-semibold', className)} // 第 97 行: 合并样式：基本字体大小和字重，并合并外部 className。
    {...props} // 第 98 行: 传递其他 props。
  />
))
ToastTitle.displayName = ToastPrimitives.Title.displayName // 第 101 行: 设置 displayName。

// 第 103 行: ToastDescription 组件，用于显示 toast 的描述内容。
const ToastDescription = React.forwardRef< // 第 103 行: 使用 React.forwardRef。
  React.ElementRef<typeof ToastPrimitives.Description>, // 第 104 行: ref 类型。
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Description> // 第 105 行: props 类型。
>(({ className, ...props }, ref) => ( // 第 106 行: 组件实现。
  <ToastPrimitives.Description // 第 107 行: 渲染 Radix UI 的 Description 组件。
    ref={ref} // 第 108 行: 传递 ref。
    className={cn('text-sm opacity-90', className)} // 第 109 行: 合并样式：基本字体大小和透明度，并合并外部 className。
    {...props} // 第 110 行: 传递其他 props。
  />
))
ToastDescription.displayName = ToastPrimitives.Description.displayName // 第 113 行: 设置 displayName。

// 第 115 行: 类型别名 ToastProps，表示 Toast 组件的属性类型。
type ToastProps = React.ComponentPropsWithoutRef<typeof Toast>

// 第 117 行: 类型别名 ToastActionElement，表示 ToastAction 组件的 React 元素类型。这在 use-toast.ts 中用于定义 action 属性的类型。
type ToastActionElement = React.ReactElement<typeof ToastAction>

// 第 119 行: 导出所有定义的类型和组件，供项目其他部分使用。
export {
  type ToastProps, // 第 120 行: 导出 ToastProps 类型。
  type ToastActionElement, // 第 121 行: 导出 ToastActionElement 类型。
  ToastProvider, // 第 122 行: 导出 ToastProvider 组件。
  ToastViewport, // 第 123 行: 导出 ToastViewport 组件。
  Toast, // 第 124 行: 导出 Toast 组件。
  ToastTitle, // 第 125 行: 导出 ToastTitle 组件。
  ToastDescription, // 第 126 行: 导出 ToastDescription 组件。
  ToastClose, // 第 127 行: 导出 ToastClose 组件。
  ToastAction, // 第 128 行: 导出 ToastAction 组件。
} 