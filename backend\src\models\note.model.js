/**
 * 笔记模型类
 * 处理与笔记相关的所有数据库操作
 * 使用 Supabase 作为数据库服务
 */
const supabase = require('../config/database');

class NoteModel {
    /**
     * 创建新笔记
     * @param {Object} noteData 笔记数据对象
     * @param {string} noteData.title 笔记标题
     * @param {string} noteData.content 笔记内容
     * @param {string[]} [noteData.tags] 笔记标签数组，默认为空数组
     * @param {string} userId 创建笔记的用户ID
     * @returns {Promise<Object>} 返回创建成功的笔记对象
     * @throws {Error} 当数据库操作失败时抛出错误
     */
    static async create(noteData, userId) {
        const { title, content, tags = [] } = noteData;

        // 向数据库插入新笔记记录
        const { data, error } = await supabase
            .from('notes')
            .insert([
                {
                    title,
                    content,
                    tags,
                    user_id: userId
                }
            ])
            .select()
            .single();

        if (error) throw error;
        return data;
    }

    /**
     * 获取用户的笔记列表，支持分页、标签过滤和搜索
     * @param {string} userId 用户ID
     * @param {Object} options 查询选项
     * @param {number} [options.page=1] 当前页码
     * @param {number} [options.limit=10] 每页显示数量
     * @param {string} [options.tag] 标签过滤
     * @param {string} [options.search] 搜索关键词
     * @returns {Promise<Object>} 返回笔记列表和分页信息
     * @throws {Error} 当数据库操作失败时抛出错误
     */
    static async list(userId, options = {}) {
        // 解构查询参数，设置默认值
        const {
            page = 1,
            limit = 10,
            tag,
            search
        } = options;

        // 构建基础查询
        // Supabase 查询构建器（Query Builder）语法说明：
        // .from('notes') - 指定查询的数据表
        // .select('*', { count: 'exact' }) - 查询所有字段，并返回精确的记录总数
        // .eq('user_id', userId) - 添加相等条件过滤（等同于 SQL: WHERE user_id = :userId）
        // .order('created_at', { ascending: false }) - 按创建时间降序排序（等同于 SQL: ORDER BY created_at DESC）
        let query = supabase
            .from('notes')
            .select('*', { count: 'exact' })
            .eq('user_id', userId)
            .order('created_at', { ascending: false });

        // 添加标签过滤条件
        // contains 方法用于数组字段的包含查询
        // 这里检查 tags 数组字段是否包含指定的 tag
        // 等同于 SQL: WHERE tags @> ARRAY[tag]
        if (tag) {
            query = query.contains('tags', [tag]);
        }

        // 添加搜索条件（标题或内容包含搜索关键词）
        // 使用 or 和 ilike 进行不区分大小写的模糊搜索
        // ilike 会自动处理 SQL 注入风险
        // 等同于 SQL: WHERE title ILIKE '%search%' OR content ILIKE '%search%'
        if (search) {
            query = query.or(`title.ilike.%${search}%,content.ilike.%${search}%`);
        }

        // 计算分页范围
        // PostgreSQL 的 LIMIT/OFFSET 分页
        // from: 起始位置（从0开始）
        // to: 结束位置（包含）
        // 例如：第1页，每页10条：from=0, to=9
        const from = (page - 1) * limit;
        const to = from + limit - 1;

        // 执行查询并获取分页数据
        // range 方法等同于 SQL 的 LIMIT 和 OFFSET
        // count 包含符合条件的总记录数（由前面 select 的 count: 'exact' 参数返回）
        const { data, error, count } = await query
            .range(from, to);

        if (error) throw error;

        // 返回格式化的结果
        // notes: 当前页的笔记数据
        // pagination: 分页信息
        //   - total: 总记录数
        //   - page: 当前页码
        //   - limit: 每页条数
        //   - total_pages: 总页数（向上取整）
        return {
            notes: data,
            pagination: {
                total: count,
                page: parseInt(page),
                limit: parseInt(limit),
                total_pages: Math.ceil(count / limit)
            }
        };
    }

    /**
     * 根据ID获取单个笔记
     * @param {string} id 笔记ID
     * @param {string} userId 用户ID（用于验证权限）
     * @returns {Promise<Object>} 返回笔记详情
     * @throws {Error} 当数据库操作失败时抛出错误
     */
    static async findById(id, userId) {
        // 查询指定ID且属于该用户的笔记
        const { data, error } = await supabase
            .from('notes')
            .select('*')
            .eq('id', id)
            .eq('user_id', userId)
            .maybeSingle();

        if (error) throw error;
        return data;
    }

    /**
     * 更新笔记信息
     * @param {string} id 笔记ID
     * @param {Object} updateData 要更新的笔记数据
     * @param {string} userId 用户ID（用于验证权限）
     * @returns {Promise<Object>} 返回更新后的笔记对象
     * @throws {Error} 当数据库操作失败时抛出错误
     */
    static async update(id, updateData, userId) {
        // 更新指定ID且属于该用户的笔记
        const { data, error } = await supabase
            .from('notes')
            .update(updateData)
            .eq('id', id)
            .eq('user_id', userId)
            .select()
            .single();

        if (error) throw error;
        return data;
    }

    /**
     * 删除笔记
     * @param {string} id 笔记ID
     * @param {string} userId 用户ID（用于验证权限）
     * @returns {Promise<void>}
     * @throws {Error} 当数据库操作失败时抛出错误
     */
    static async delete(id, userId) {
        // 删除指定ID且属于该用户的笔记
        const { error } = await supabase
            .from('notes')
            .delete()
            .eq('id', id)
            .eq('user_id', userId);

        if (error) throw error;
    }

    /**
     * 获取用户所有笔记中使用的标签统计
     * @param {string} userId 用户ID
     * @returns {Promise<Array<{name: string, count: number}>>} 返回标签名称和使用次数的数组
     * @throws {Error} 当数据库操作失败时抛出错误
     */
    static async getTags(userId) {
        // 获取用户所有笔记的标签
        const { data, error } = await supabase
            .from('notes')
            .select('tags')
            .eq('user_id', userId);

        if (error) throw error;

        // 统计每个标签的使用次数
        const tagCounts = {};
        data.forEach(note => {
            note.tags.forEach(tag => {
                tagCounts[tag] = (tagCounts[tag] || 0) + 1;
            });
        });

        // 将统计结果转换为数组格式
        return Object.entries(tagCounts).map(([name, count]) => ({
            name,
            count
        }));
    }
}

module.exports = NoteModel; 