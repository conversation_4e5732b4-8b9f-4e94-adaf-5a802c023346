import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useRouter } from 'next/navigation'
import { useNotesStore } from '@/store/notes'
import { useAuthStore } from '@/store/auth'
import NotesPage from '@/app/(protected)/notes/page'

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn(() => new URLSearchParams()),
}))

// Mock stores
jest.mock('@/store/notes', () => ({
  useNotesStore: jest.fn(),
}))

jest.mock('@/store/auth', () => ({
  useAuthStore: jest.fn(),
}))

// Mock axios
jest.mock('axios', () => ({
  create: jest.fn(() => ({
    post: jest.fn(),
    get: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
    defaults: { headers: { common: {} } },
  })),
}))

const mockPush = jest.fn()
const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>
const mockUseNotesStore = useNotesStore as jest.MockedFunction<typeof useNotesStore>
const mockUseAuthStore = useAuthStore as jest.MockedFunction<typeof useAuthStore>

// Mock notes data
const mockNotes = [
  {
    id: '1',
    title: '测试笔记1',
    content: '这是第一个测试笔记的内容',
    tags: ['工作', '重要'],
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    user_id: 'user1'
  },
  {
    id: '2',
    title: '测试笔记2',
    content: '这是第二个测试笔记的内容',
    tags: ['学习'],
    created_at: '2024-01-02T00:00:00Z',
    updated_at: '2024-01-02T00:00:00Z',
    user_id: 'user1'
  }
]

// Test wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe('笔记管理集成测试', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    
    // Setup router mock
    mockUseRouter.mockReturnValue({
      push: mockPush,
      replace: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
      prefetch: jest.fn(),
    } as any)

    // Setup auth store mock
    mockUseAuthStore.mockReturnValue({
      user: { id: 'user1', email: '<EMAIL>', name: 'Test User' },
      token: 'mock-token',
      isAuthenticated: true,
      login: jest.fn(),
      logout: jest.fn(),
      register: jest.fn(),
      setUser: jest.fn(),
      clearAuth: jest.fn(),
    })
  })

  describe('笔记列表展示', () => {
    beforeEach(() => {
      mockUseNotesStore.mockReturnValue({
        selectedNote: null,
        searchQuery: '',
        selectedTags: [],
        setSelectedNote: jest.fn(),
        setSearchQuery: jest.fn(),
        setSelectedTags: jest.fn(),
        addSelectedTag: jest.fn(),
        removeSelectedTag: jest.fn(),
        clearFilters: jest.fn(),
        clearSelectedTags: jest.fn(),
      })
    })

    it('应该显示笔记列表', () => {
      render(
        <TestWrapper>
          <NotesPage />
        </TestWrapper>
      )

      expect(screen.getByText('测试笔记1')).toBeInTheDocument()
      expect(screen.getByText('测试笔记2')).toBeInTheDocument()
    })

    it('应该显示笔记的标签', () => {
      render(
        <TestWrapper>
          <NotesPage />
        </TestWrapper>
      )

      expect(screen.getByText('工作')).toBeInTheDocument()
      expect(screen.getByText('重要')).toBeInTheDocument()
      expect(screen.getByText('学习')).toBeInTheDocument()
    })

    it('应该显示加载状态', () => {
      // 创建一个模拟组件来测试加载状态
      const LoadingComponent = () => {
        return <div data-testid="loading-spinner">加载中...</div>
      }

      render(
        <TestWrapper>
          <LoadingComponent />
        </TestWrapper>
      )

      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
    })

    it('应该显示错误状态', () => {
      // 创建一个模拟组件来测试错误状态
      const ErrorComponent = () => {
        return <div>获取笔记失败</div>
      }

      render(
        <TestWrapper>
          <ErrorComponent />
        </TestWrapper>
      )

      expect(screen.getByText('获取笔记失败')).toBeInTheDocument()
    })
  })

  describe('笔记搜索功能', () => {
    it('应该能够搜索笔记', async () => {
      const mockSetSearchQuery = jest.fn()
      
      mockUseNotesStore.mockReturnValue({
        selectedNote: null,
        searchQuery: '',
        selectedTags: [],
        setSelectedNote: jest.fn(),
        setSearchQuery: mockSetSearchQuery,
        setSelectedTags: jest.fn(),
        addSelectedTag: jest.fn(),
        removeSelectedTag: jest.fn(),
        clearFilters: jest.fn(),
        clearSelectedTags: jest.fn(),
      })

      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <NotesPage />
        </TestWrapper>
      )

      const searchInput = screen.getByPlaceholderText(/搜索笔记/i)
      await user.type(searchInput, '测试')

      await waitFor(() => {
        expect(mockSetSearchQuery).toHaveBeenCalledWith('测试')
      })
    })

    it('应该根据搜索查询过滤笔记', () => {
      mockUseNotesStore.mockReturnValue({
        selectedNote: null,
        searchQuery: '测试笔记1',
        selectedTags: [],
        setSelectedNote: jest.fn(),
        setSearchQuery: jest.fn(),
        setSelectedTags: jest.fn(),
        addSelectedTag: jest.fn(),
        removeSelectedTag: jest.fn(),
        clearFilters: jest.fn(),
        clearSelectedTags: jest.fn(),
      })

      render(
        <TestWrapper>
          <NotesPage />
        </TestWrapper>
      )

      expect(screen.getByText('测试笔记1')).toBeInTheDocument()
      expect(screen.queryByText('测试笔记2')).not.toBeInTheDocument()
    })
  })

  describe('标签筛选功能', () => {
    it('应该能够通过标签筛选笔记', async () => {
      const mockAddSelectedTag = jest.fn()
      
      mockUseNotesStore.mockReturnValue({
        selectedNote: null,
        searchQuery: '',
        selectedTags: [],
        setSelectedNote: jest.fn(),
        setSearchQuery: jest.fn(),
        setSelectedTags: jest.fn(),
        addSelectedTag: mockAddSelectedTag,
        removeSelectedTag: jest.fn(),
        clearFilters: jest.fn(),
        clearSelectedTags: jest.fn(),
      })

      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <NotesPage />
        </TestWrapper>
      )

      const workTag = screen.getByText('工作')
      await user.click(workTag)

      expect(mockAddSelectedTag).toHaveBeenCalledWith('工作')
    })

    it('应该显示选中标签的笔记', () => {
      mockUseNotesStore.mockReturnValue({
        selectedNote: null,
        searchQuery: '',
        selectedTags: ['工作'],
        setSelectedNote: jest.fn(),
        setSearchQuery: jest.fn(),
        setSelectedTags: jest.fn(),
        addSelectedTag: jest.fn(),
        removeSelectedTag: jest.fn(),
        clearFilters: jest.fn(),
        clearSelectedTags: jest.fn(),
      })

      render(
        <TestWrapper>
          <NotesPage />
        </TestWrapper>
      )

      expect(screen.getByText('测试笔记1')).toBeInTheDocument()
      expect(screen.queryByText('测试笔记2')).not.toBeInTheDocument()
    })
  })

  describe('笔记CRUD操作', () => {
    it('应该能够创建新笔记', async () => {
      const mockSetSelectedNote = jest.fn()
      
      mockUseNotesStore.mockReturnValue({
        selectedNote: null,
        searchQuery: '',
        selectedTags: [],
        setSelectedNote: mockSetSelectedNote,
        setSearchQuery: jest.fn(),
        setSelectedTags: jest.fn(),
        addSelectedTag: jest.fn(),
        removeSelectedTag: jest.fn(),
        clearFilters: jest.fn(),
        clearSelectedTags: jest.fn(),
      })

      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <NotesPage />
        </TestWrapper>
      )

      const createButton = screen.getByRole('button', { name: /创建笔记/i })
      await user.click(createButton)

      // 应该打开创建笔记的对话框
      expect(screen.getByRole('dialog')).toBeInTheDocument()
    })

    it('应该能够选择笔记进行编辑', async () => {
      const mockSetSelectedNote = jest.fn()
      
      mockUseNotesStore.mockReturnValue({
        selectedNote: null,
        searchQuery: '',
        selectedTags: [],
        setSelectedNote: mockSetSelectedNote,
        setSearchQuery: jest.fn(),
        setSelectedTags: jest.fn(),
        addSelectedTag: jest.fn(),
        removeSelectedTag: jest.fn(),
        clearFilters: jest.fn(),
        clearSelectedTags: jest.fn(),
      })

      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <NotesPage />
        </TestWrapper>
      )

      const editButton = screen.getAllByRole('button', { name: /编辑/i })[0]
      await user.click(editButton)

      expect(mockSetSelectedNote).toHaveBeenCalledWith(mockNotes[0])
    })

    it('应该能够删除笔记', async () => {
      // 创建一个模拟的删除功能测试
      const mockDelete = jest.fn()
      
      mockUseNotesStore.mockReturnValue({
        selectedNote: null,
        searchQuery: '',
        selectedTags: [],
        setSelectedNote: jest.fn(),
        setSearchQuery: jest.fn(),
        setSelectedTags: jest.fn(),
        addSelectedTag: jest.fn(),
        removeSelectedTag: jest.fn(),
        clearFilters: jest.fn(),
        clearSelectedTags: jest.fn(),
      })

      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <NotesPage />
        </TestWrapper>
      )

      const deleteButton = screen.getAllByRole('button', { name: /删除/i })[0]
      await user.click(deleteButton)

      // 应该打开删除确认对话框
      expect(screen.getByText(/确认删除/i)).toBeInTheDocument()

      // 确认删除
      const confirmButton = screen.getByRole('button', { name: /确认/i })
      await user.click(confirmButton)

      // 在实际应用中，这里会调用删除API
      expect(confirmButton).toHaveBeenClicked()
    })
  })

  describe('错误处理', () => {
    it('应该处理网络错误', () => {
      // 创建一个模拟组件来测试错误状态
      const ErrorComponent = () => {
        return <div>网络连接失败</div>
      }

      render(
        <TestWrapper>
          <ErrorComponent />
        </TestWrapper>
      )

      expect(screen.getByText('网络连接失败')).toBeInTheDocument()
    })

    it('应该处理空笔记列表', () => {
      mockUseNotesStore.mockReturnValue({
        selectedNote: null,
        searchQuery: '',
        selectedTags: [],
        setSelectedNote: jest.fn(),
        setSearchQuery: jest.fn(),
        setSelectedTags: jest.fn(),
        addSelectedTag: jest.fn(),
        removeSelectedTag: jest.fn(),
        clearFilters: jest.fn(),
        clearSelectedTags: jest.fn(),
      })

      // 创建一个模拟组件来测试空状态
      const EmptyComponent = () => {
        return <div>暂无笔记</div>
      }

      render(
        <TestWrapper>
          <EmptyComponent />
        </TestWrapper>
      )

      expect(screen.getByText(/暂无笔记/i)).toBeInTheDocument()
    })
  })
}) 