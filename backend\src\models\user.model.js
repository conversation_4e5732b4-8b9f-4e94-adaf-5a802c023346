const supabase = require('../config/database');
const { hashPassword } = require('../utils/auth');

class UserModel {
    static async findByEmail(email) {
        const { data, error } = await supabase
            .from('users')
            .select('*')
            .eq('email', email)
            .maybeSingle();

        if (error) throw error;
        return data;
    }

    static async create(userData) {
        const { email, password, name } = userData;

        // 检查邮箱是否已存在
        const existingUser = await this.findByEmail(email);
        if (existingUser) {
            throw new Error('Email already exists');
        }

        // 密码加密
        const password_hash = await hashPassword(password);

        // 创建用户
        const { data, error } = await supabase
            .from('users')
            .insert([
                {
                    email,
                    password_hash,
                    name
                }
            ])
            .select();

        if (error) throw error;
        if (!data || data.length === 0) {
            throw new Error('Failed to create user');
        }
        
        return data[0];
    }

    static async findById(id) {
        const { data, error } = await supabase
            .from('users')
            .select('*')
            .eq('id', id)
            .maybeSingle();

        if (error) throw error;
        return data;
    }

    static async update(id, updateData) {
        const { data, error } = await supabase
            .from('users')
            .update(updateData)
            .eq('id', id)
            .select()
            .maybeSingle();

        if (error) throw error;
        return data;
    }
}

module.exports = UserModel;