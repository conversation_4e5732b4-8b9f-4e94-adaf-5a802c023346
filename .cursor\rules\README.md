# 个人笔记管理系统

一个使用现代技术栈开发的个人笔记管理系统，采用前后端分离架构。

## 技术栈

### 前端
- **核心框架**: 
  - Next.js 13+ (App Router)
  - React 18+
  - TypeScript
- **UI 框架和样式**: 
  - Shadcn UI (基于 Tailwind CSS 的现代 UI 组件库)
  - Tailwind CSS (原子化 CSS)
  - CSS Modules (组件级样式)
- **状态管理**: 
  - Zustand (轻量级状态管理)
  - React Query (服务端状态管理)
- **工具库**:
  - Axios (HTTP 请求)
  - Zod (类型验证)
  - React Hook Form (表单处理)
  - date-fns (日期处理)
  - lucide-react (图标库)
- **开发工具**:
  - ESLint (代码规范)
  - Prettier (代码格式化)
  - <PERSON><PERSON> (Git hooks)

### 后端
- **核心框架**: Node.js + Express.js
- **数据库**: Supabase
- **认证**: JWT
- **API文档**: Swagger/OpenAPI
- **工具库**:
  - cors (跨域处理)
  - helmet (安全头)
  - morgan (日志)
  - express-validator (数据验证)

## 项目结构

### 前端结构
```
frontend/
├── src/
│   ├── app/                    # Next.js App Router 页面
│   ├── components/             # React 组件
│   │   ├── ui/                # 基础 UI 组件
│   │   └── common/            # 通用业务组件
│   ├── hooks/                 # 自定义 Hooks
│   ├── lib/                   # 工具函数
│   ├── store/                 # Zustand 状态管理
│   ├── types/                 # TypeScript 类型定义
│   └── styles/                # 全局样式
```

### 后端结构
```
backend/
├── src/
│   ├── config/                # 配置文件
│   │   ├── database.js       # 数据库配置
│   │   └── auth.js          # 认证配置
│   ├── routes/               # 路由层
│   ├── controllers/          # 控制器层
│   ├── services/            # 业务逻辑层
│   ├── models/              # 数据模型
│   ├── middlewares/         # 中间件
│   ├── utils/               # 工具函数
│   └── app.js              # 应用入口文件
```

## 功能模块

### 1. 用户认证模块
- 用户注册
- 用户登录
- JWT 认证
- 用户信息管理

### 2. 笔记管理模块
- 笔记 CRUD 操作
- 笔记分类
- 标签管理
- 笔记搜索

### 3. 用户界面模块
- 响应式设计
- 深色/浅色主题
- 富文本编辑器
- 标签筛选

## API 设计

### 认证相关
```
POST   /api/auth/register    - 用户注册
POST   /api/auth/login       - 用户登录
POST   /api/auth/logout      - 用户登出
```

### 笔记相关
```
GET    /api/notes           - 获取所有笔记
POST   /api/notes           - 创建笔记
GET    /api/notes/:id       - 获取单个笔记
PUT    /api/notes/:id       - 更新笔记
DELETE /api/notes/:id       - 删除笔记
```

### 用户相关
```
GET    /api/users/profile   - 获取用户信息
PUT    /api/users/profile   - 更新用户信息
```

## 数据库设计

### Users 表
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  name VARCHAR(255),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### Notes 表
```sql
CREATE TABLE notes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id),
  title VARCHAR(255) NOT NULL,
  content TEXT,
  tags TEXT[],
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

## 开发计划

### 第一阶段：项目初始化与基础设置
1. 创建前端项目
   - 设置 Next.js
   - 配置 TypeScript
   - 安装必要依赖
   - 配置 Tailwind CSS 和 Shadcn UI

2. 创建后端项目
   - 设置 Express.js
   - 配置开发环境
   - 安装必要依赖
   - 设置基础中间件

3. 数据库设置
   - 创建 Supabase 项目
   - 设计并创建数据表
   - 配置数据库连接

### 第二阶段：核心功能开发
1. 后端开发
   - 实现用户认证
   - 开发笔记 CRUD API
   - 实现中间件

2. 前端开发
   - 实现用户界面
   - 集成 API
   - 实现状态管理

### 第三阶段：功能完善
1. 优化用户体验
   - 添加加载状态
   - 实现错误处理
   - 添加提示信息

2. 性能优化
   - 实现数据缓存
   - 优化组件渲染
   - 添加分页功能

### 第四阶段：测试与部署
1. 测试
   - 单元测试
   - 集成测试
   - 用户测试

2. 部署
   - 配置生产环境
   - 部署应用
   - 监控设置

## 运行项目

### 前端
```bash
# 安装依赖
cd frontend
npm install

# 开发环境运行
npm run dev

# 构建生产版本
npm run build
npm start
```

### 后端
```bash
# 安装依赖
cd backend
npm install

# 开发环境运行
npm run dev

# 生产环境运行
npm start
```

## 环境变量配置

### 前端 (.env.local)
```
NEXT_PUBLIC_API_URL=http://localhost:3000
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
```

### 后端 (.env)
```
PORT=3000
JWT_SECRET=your-jwt-secret
SUPABASE_URL=your-supabase-url
SUPABASE_KEY=your-supabase-service-role-key
```

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 许可证

MIT License - 详见 LICENSE 文件 