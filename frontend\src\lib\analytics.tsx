'use client'

import { Analytics } from '@vercel/analytics/react'
import React from 'react'

// Web Vitals 性能指标收集
export function reportWebVitals(metric: any) {
  const { id, name, label, value } = metric

  // 发送到分析服务
  console.log('性能指标:', {
    metric: name,
    id,
    value: Math.round(name === 'CLS' ? value * 1000 : value),
    label: label === 'web-vital' ? 'Web Vital' : 'Custom',
  })
}

// 用户行为跟踪
export function trackEvent(eventName: string, properties?: Record<string, any>) {
  // 发送到分析服务
  console.log('用户事件:', {
    event: eventName,
    properties,
    timestamp: new Date().toISOString(),
  })
}

// 错误跟踪
export function trackError(error: Error, context?: Record<string, any>) {
  // 发送到错误跟踪服务
  console.error('应用错误:', {
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
    },
    context,
    timestamp: new Date().toISOString(),
  })
}

// 性能监控组件
export function AnalyticsProvider({ children }: { children: React.ReactNode }) {
  return (
    <div>
      {children}
      <Analytics />
    </div>
  )
} 