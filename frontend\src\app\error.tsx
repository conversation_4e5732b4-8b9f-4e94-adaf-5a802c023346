'use client'

import { useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { trackError } from '@/lib/analytics'

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // 使用 useEffect Hook 在组件挂载或 error prop 变化时执行副作用。
    // 在这里，它用于将捕获到的错误信息发送到分析服务进行记录。
    // 这是一个常见的模式，用于在错误发生时进行监控和调试。
    trackError(error)
  }, [error])

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      {/* 主错误标题 */}
      <h1 className="text-4xl font-bold mb-4">出错了</h1>
      {/* 更详细的错误描述信息 */}
      <p className="text-xl text-muted-foreground mb-8">
        抱歉，服务器出现了一些问题
      </p>
      {/* 
        重试按钮。
        点击此按钮会调用 Next.js 提供的 reset 函数，
        尝试重新渲染出错的组件树部分。
        如果错误是暂时的（例如网络波动），这可能会成功。
      */}
      <Button onClick={reset}>
        重试
      </Button>
    </div>
  )
} 