/* Tiptap 富文本编辑器样式 */

.prose-editor .ProseMirror {
  outline: none;
  padding: 1rem;
  min-height: 300px;
  line-height: 1.6;
  color: rgb(55 65 81);
}

.dark .prose-editor .ProseMirror {
  color: rgb(229 231 235);
}

/* 占位符样式 */
.prose-editor .ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: rgb(156 163 175);
  pointer-events: none;
  height: 0;
}

.dark .prose-editor .ProseMirror p.is-editor-empty:first-child::before {
  color: rgb(107 114 128);
}

/* 标题样式 */
.prose-editor .ProseMirror h1 {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1.2;
  margin-top: 2rem;
  margin-bottom: 1rem;
  color: rgb(17 24 39);
}

.dark .prose-editor .ProseMirror h1 {
  color: rgb(243 244 246);
}

.prose-editor .ProseMirror h2 {
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.3;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  color: rgb(31 41 55);
}

.dark .prose-editor .ProseMirror h2 {
  color: rgb(229 231 235);
}

.prose-editor .ProseMirror h3 {
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.4;
  margin-top: 1.25rem;
  margin-bottom: 0.5rem;
  color: rgb(55 65 81);
}

.dark .prose-editor .ProseMirror h3 {
  color: rgb(209 213 219);
}

/* 段落样式 */
.prose-editor .ProseMirror p {
  margin-bottom: 1rem;
}

/* 列表样式 */
.prose-editor .ProseMirror ul,
.prose-editor .ProseMirror ol {
  padding-left: 1.5rem;
  margin-bottom: 1rem;
}

.prose-editor .ProseMirror li {
  margin-bottom: 0.25rem;
}

.prose-editor .ProseMirror ul li {
  list-style-type: disc;
}

.prose-editor .ProseMirror ol li {
  list-style-type: decimal;
}

/* 引用样式 */
.prose-editor .ProseMirror blockquote {
  border-left: 4px solid rgb(59 130 246);
  padding-left: 1rem;
  margin: 1.5rem 0;
  font-style: italic;
  color: rgb(75 85 99);
  background-color: rgb(249 250 251);
  padding: 1rem;
  border-radius: 0.5rem;
}

.dark .prose-editor .ProseMirror blockquote {
  border-left-color: rgb(59 130 246);
  color: rgb(156 163 175);
  background-color: rgb(31 41 55);
}

/* 代码样式 */
.prose-editor .ProseMirror code {
  background-color: rgb(243 244 246);
  color: rgb(239 68 68);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
}

.dark .prose-editor .ProseMirror code {
  background-color: rgb(55 65 81);
  color: rgb(248 113 113);
}

/* 代码块样式 */
.prose-editor .ProseMirror pre {
  background-color: rgb(249 250 251);
  border: 1px solid rgb(229 231 235);
  border-radius: 0.5rem;
  padding: 1rem;
  margin: 1rem 0;
  overflow-x: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
}

.dark .prose-editor .ProseMirror pre {
  background-color: rgb(31 41 55);
  border-color: rgb(75 85 99);
}

.prose-editor .ProseMirror pre code {
  background: none;
  color: inherit;
  padding: 0;
  border-radius: 0;
}

/* 高亮样式 */
.prose-editor .ProseMirror mark {
  background-color: rgb(254 240 138);
  color: rgb(146 64 14);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
}

.dark .prose-editor .ProseMirror mark {
  background-color: rgb(180 83 9);
  color: rgb(254 215 170);
}

/* 链接样式 */
.prose-editor .ProseMirror a {
  color: rgb(59 130 246);
  text-decoration: underline;
  cursor: pointer;
}

.prose-editor .ProseMirror a:hover {
  color: rgb(37 99 235);
}

.dark .prose-editor .ProseMirror a {
  color: rgb(96 165 250);
}

.dark .prose-editor .ProseMirror a:hover {
  color: rgb(59 130 246);
}

/* 图片样式 */
.prose-editor .ProseMirror img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: 1rem 0;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
}

/* 表格样式 */
.prose-editor .ProseMirror table {
  border-collapse: collapse;
  margin: 1rem 0;
  width: 100%;
  border: 1px solid rgb(229 231 235);
  border-radius: 0.5rem;
  overflow: hidden;
}

.dark .prose-editor .ProseMirror table {
  border-color: rgb(75 85 99);
}

.prose-editor .ProseMirror th,
.prose-editor .ProseMirror td {
  border: 1px solid rgb(229 231 235);
  padding: 0.5rem 0.75rem;
  text-align: left;
  vertical-align: top;
}

.dark .prose-editor .ProseMirror th,
.dark .prose-editor .ProseMirror td {
  border-color: rgb(75 85 99);
}

.prose-editor .ProseMirror th {
  background-color: rgb(249 250 251);
  font-weight: 600;
  color: rgb(55 65 81);
}

.dark .prose-editor .ProseMirror th {
  background-color: rgb(55 65 81);
  color: rgb(229 231 235);
}

/* 选中状态 */
.prose-editor .ProseMirror .selectedCell:after {
  z-index: 2;
  position: absolute;
  content: "";
  left: 0; right: 0; top: 0; bottom: 0;
  background: rgba(59, 130, 246, 0.1);
  pointer-events: none;
}

/* 焦点状态 */
.prose-editor .ProseMirror:focus {
  outline: none;
}

/* 水平分割线 */
.prose-editor .ProseMirror hr {
  border: none;
  border-top: 2px solid rgb(229 231 235);
  margin: 2rem 0;
}

.dark .prose-editor .ProseMirror hr {
  border-top-color: rgb(75 85 99);
}

/* 响应式调整 */
@media (max-width: 640px) {
  .prose-editor .ProseMirror {
    padding: 0.75rem;
  }
  
  .prose-editor .ProseMirror h1 {
    font-size: 1.75rem;
  }
  
  .prose-editor .ProseMirror h2 {
    font-size: 1.375rem;
  }
  
  .prose-editor .ProseMirror h3 {
    font-size: 1.125rem;
  }
}
