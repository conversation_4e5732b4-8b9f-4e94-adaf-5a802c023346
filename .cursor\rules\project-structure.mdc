---
description: 
globs: 
alwaysApply: true
---
powershell 不支持 看起来 PowerShell 不支持 && 操作符，让我们分开执行命令 用; 分号 连接命令



# Notes API 项目结构说明

## 一、项目文件结构

```
backend/
├── src/
│   ├── app.js                         # 应用程序入口文件
│   ├── config/
│   │   ├── database.js               # Supabase数据库配置
│   │   └── init.sql                  # 数据库初始化SQL
│   │
│   ├── controllers/
│   │   ├── auth.controller.js        # 用户认证控制器
│   │   └── notes.controller.js       # 笔记管理控制器
│   │
│   ├── models/
│   │   └── notes.model.js            # 笔记数据模型
│   │
│   ├── routes/
│   │   ├── auth.routes.js            # 认证相关路由
│   │   └── notes.routes.js           # 笔记相关路由
│   │
│   ├── services/
│   │   ├── auth.service.js           # 认证业务逻辑
│   │   └── notes.service.js          # 笔记业务逻辑
│   │
│   ├── middlewares/
│   │   ├── auth.middleware.js        # 认证中间件
│   │   ├── error.middleware.js       # 错误处理中间件
│   │   └── validator.middleware.js    # 数据验证中间件
│   │
│   └── utils/
│       ├── jwt.js                    # JWT工具函数
│       └── validation.js             # 数据验证工具
│
├── package.json                       # 项目配置文件
└── README.md                         # 项目说明文档

```

## 二、文件说明

### 1. 入口文件
- **app.js**
  ```javascript
  // 主要功能：
  - Express应用程序配置
  - 中间件设置 (cors, helmet, morgan)
  - 路由注册
  - 错误处理
  - 服务器启动
  ```

### 2. 配置文件 (config/)
- **database.js**
  ```javascript
  // Supabase客户端配置
  - 创建Supabase客户端实例
  - 环境变量验证
  - 数据库连接管理
  ```

- **init.sql**
  ```sql
  // 数据库初始化脚本
  - 创建users表
  - 创建notes表
  - 设置触发器
  - 创建UUID扩展
  ```

### 3. 数据库表结构

#### users表
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    name VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### notes表
```sql
CREATE TABLE notes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    content TEXT,
    tags TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### 4. API路由

#### 认证路由 (auth.routes.js)
```javascript
POST   /api/auth/register    // 用户注册
POST   /api/auth/login       // 用户登录
POST   /api/auth/refresh     // 刷新token
```

#### 笔记路由 (notes.routes.js)
```javascript
GET    /api/notes           // 获取笔记列表
POST   /api/notes           // 创建新笔记
GET    /api/notes/:id       // 获取单个笔记
PUT    /api/notes/:id       // 更新笔记
DELETE /api/notes/:id       // 删除笔记
```

## 三、关键依赖说明

```json
{
  "dependencies": {
    "@supabase/supabase-js": "^2.49.8",  // Supabase客户端
    "bcryptjs": "^3.0.2",                // 密码加密
    "cors": "^2.8.5",                    // 跨域处理
    "dotenv": "^16.5.0",                 // 环境变量
    "express": "^5.1.0",                 // Web框架
    "express-validator": "^7.2.1",       // 请求验证
    "helmet": "^8.1.0",                  // 安全中间件
    "jsonwebtoken": "^9.0.2",            // JWT处理
    "morgan": "^1.10.0"                  // 日志中间件
  }
}
```

## 四、环境变量配置

```env
# 服务器配置
PORT=3000
NODE_ENV=development

# Supabase配置
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_key

# JWT配置
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=24h
```

## 五、API响应格式

### 成功响应
```json
{
  "status": "success",
  "data": {
    "id": "uuid-v4",
    "title": "笔记标题",
    "content": "笔记内容",
    "tags": ["标签1", "标签2"],
    "created_at": "2024-03-15T10:00:00Z",
    "updated_at": "2024-03-15T10:00:00Z"
  }
}
```

### 错误响应
```json
{
  "status": "error",
  "message": "错误信息",
  "error": {
    "code": "ERROR_CODE",
    "details": {}
  }
}
```














