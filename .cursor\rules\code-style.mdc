---
description:
globs:
alwaysApply: false
---
# 代码风格指南

## TypeScript

1. 总是使用类型注解
2. 避免使用`any`类型
3. 使用接口（Interface）定义对象类型
4. 使用类型别名（Type Alias）定义联合类型和交叉类型

## React组件

1. 使用函数组件和Hooks
2. Props类型定义放在组件同一文件中
3. 组件文件使用`.tsx`扩展名
4. 小型组件使用箭头函数，大型组件使用命名函数

示例:
```tsx
interface ButtonProps {
  label: string;
  onClick: () => void;
  variant?: 'primary' | 'secondary';
}

const Button = ({ label, onClick, variant = 'primary' }: ButtonProps) => {
  return (
    <button 
      onClick={onClick}
      className={`btn ${variant}`}
    >
      {label}
    </button>
  );
};
```

## API路由

1. 使用RESTful设计原则
2. 统一错误处理
3. 请求验证
4. 适当的HTTP状态码

## 数据库操作

1. 使用Supabase客户端
2. 总是处理错误情况
3. 使用事务保证数据一致性
4. 编写清晰的数据库迁移文件
