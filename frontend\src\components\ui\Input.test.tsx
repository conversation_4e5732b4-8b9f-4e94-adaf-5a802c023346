import { render, screen, fireEvent } from '@testing-library/react';
import { Input } from './input';

describe('Input 组件', () => {
  it('应该正确渲染输入框', () => {
    render(<Input placeholder="请输入" />);
    expect(screen.getByPlaceholderText('请输入')).toBeInTheDocument();
  });

  it('应该正确处理值的变化', () => {
    const handleChange = jest.fn();
    render(<Input onChange={handleChange} />);
    
    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: '测试文本' } });
    
    expect(handleChange).toHaveBeenCalled();
    expect(input).toHaveValue('测试文本');
  });

  it('禁用状态下不应该可编辑', () => {
    render(<Input disabled />);
    const input = screen.getByRole('textbox');
    
    expect(input).toBeDisabled();
    expect(input).toHaveClass('disabled:cursor-not-allowed');
  });

  it('应该正确应用自定义类名', () => {
    const customClass = 'custom-class';
    render(<Input className={customClass} />);
    
    expect(screen.getByRole('textbox')).toHaveClass(customClass);
  });

  it('应该正确处理不同类型的输入', () => {
    render(<Input type="password" data-testid="password-input" />);
    expect(screen.getByTestId('password-input')).toHaveAttribute('type', 'password');
  });

  it('应该正确处理焦点事件', () => {
    const handleFocus = jest.fn();
    const handleBlur = jest.fn();
    
    render(<Input onFocus={handleFocus} onBlur={handleBlur} />);
    const input = screen.getByRole('textbox');
    
    fireEvent.focus(input);
    expect(handleFocus).toHaveBeenCalledTimes(1);
    
    fireEvent.blur(input);
    expect(handleBlur).toHaveBeenCalledTimes(1);
  });
}); 