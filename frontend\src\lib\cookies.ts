'use client'

import Cookies from 'js-cookie'

const TOKEN_NAME = 'token'
const REFRESH_TOKEN_NAME = 'refreshToken'

export function getToken(): string | undefined {
  return Cookies.get(TOKEN_NAME)
}

export function setToken(token: string): void {
  Cookies.set(TOKEN_NAME, token, { expires: 7 })
}

export function removeToken(): void {
  Cookies.remove(TOKEN_NAME)
}

export function getRefreshToken(): string | undefined {
  return Cookies.get(REFRESH_TOKEN_NAME)
}

export function setRefreshToken(token: string): void {
  Cookies.set(REFRESH_TOKEN_NAME, token, { expires: 30 })
}

export function removeRefreshToken(): void {
  Cookies.remove(REFRESH_TOKEN_NAME)
} 