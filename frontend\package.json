{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest --testPathPattern=integration", "test:unit": "jest --testPathPattern=__tests__ --testPathIgnorePatterns=integration", "test:all": "jest --coverage --testPathPattern=__tests__"}, "dependencies": {"@next/bundle-analyzer": "^15.3.3", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@stagewise-plugins/react": "^0.4.7", "@stagewise/toolbar-next": "^0.4.7", "@types/js-cookie": "^3.0.6", "@vercel/analytics": "^1.5.0", "critters": "^0.0.23", "js-cookie": "^3.0.5", "next": "15.3.3", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/eslintrc": "^3", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "@tailwindcss/postcss": "^4", "@tanstack/react-query": "^5.79.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "eslint": "^9", "eslint-config-next": "15.3.3", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "lucide-react": "^0.511.0", "prettier": "^3.5.3", "react-hook-form": "^7.56.4", "tailwind-merge": "^3.3.0", "tailwindcss": "^4", "ts-node": "^10.9.2", "tw-animate-css": "^1.3.2", "typescript": "^5", "zod": "^3.25.41", "zustand": "^5.0.5"}}