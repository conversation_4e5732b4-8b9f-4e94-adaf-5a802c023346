const AuthService = require('../services/auth.service');
const { validationResult } = require('express-validator');

class AuthController {
    /**
     * 用户注册
     */
    static async register(req, res) {
        try {
            // 验证请求数据
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    status: 'error',
                    errors: errors.array()
                });
            }

            const { email, password, name } = req.body;
            const result = await AuthService.register({ email, password, name });

            res.status(201).json({
                status: 'success',
                data: result
            });
        } catch (error) {
            res.status(400).json({
                status: 'error',
                message: error.message
            });
        }
    }

    /**
     * 用户登录
     */
    static async login(req, res) {
        try {
            // 验证请求数据
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    status: 'error',
                    errors: errors.array()
                });
            }

            const { email, password } = req.body;
            const result = await AuthService.login(email, password);

            res.json({
                status: 'success',
                data: result
            });
        } catch (error) {
            res.status(401).json({
                status: 'error',
                message: error.message
            });
        }
    }

    /**
     * 获取当前用户信息
     */
    static async getCurrentUser(req, res) {
        try {
            const userId = req.user.id;
            const user = await AuthService.getCurrentUser(userId);

            res.json({
                status: 'success',
                data: { user }
            });
        } catch (error) {
            res.status(400).json({
                status: 'error',
                message: error.message
            });
        }
    }
}

module.exports = AuthController; 