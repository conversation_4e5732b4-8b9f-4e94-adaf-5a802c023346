const NoteModel = require('../models/note.model');

class NotesService {
    /**
     * 创建新笔记
     * @param {Object} noteData 笔记数据
     * @param {string} userId 用户ID
     * @returns {Promise<Object>} 创建的笔记
     */
    static async createNote(noteData, userId) {
        try {
            // 数据验证和处理
            const { title, content, tags = [] } = noteData;

            if (!title || title.trim().length === 0) {
                throw new Error('笔记标题不能为空');
            }

            // 处理标签：去重、去空、转小写
            const processedTags = [...new Set(tags
                .map(tag => tag.trim().toLowerCase())
                .filter(tag => tag.length > 0)
            )];

            // 创建笔记
            const note = await NoteModel.create({
                title: title.trim(),
                content: content ? content.trim() : '',
                tags: processedTags
            }, userId);

            return note;
        } catch (error) {
            throw error;
        }
    }

    /**
     * 获取笔记列表
     * @param {string} userId 用户ID
     * @param {Object} options 查询选项
     * @returns {Promise<Object>} 笔记列表和分页信息
     */
    static async getNotes(userId, options = {}) {
        try {
            const {
                page = 1,
                limit = 10,
                tag,
                search,
                sortBy = 'created_at',
                sortOrder = 'desc'
            } = options;

            // 验证分页参数
            const validatedPage = Math.max(1, parseInt(page));
            const validatedLimit = Math.min(100, Math.max(1, parseInt(limit)));

            // 验证排序参数
            const allowedSortFields = ['created_at', 'updated_at', 'title'];
            const validatedSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'created_at';
            const validatedSortOrder = ['asc', 'desc'].includes(sortOrder.toLowerCase()) ? sortOrder : 'desc';

            return await NoteModel.list(userId, {
                page: validatedPage,
                limit: validatedLimit,
                tag,
                search,
                sortBy: validatedSortBy,
                sortOrder: validatedSortOrder
            });
        } catch (error) {
            throw error;
        }
    }

    /**
     * 获取单个笔记
     * @param {string} noteId 笔记ID
     * @param {string} userId 用户ID
     * @returns {Promise<Object>} 笔记详情
     */
    static async getNoteById(noteId, userId) {
        try {
            const note = await NoteModel.findById(noteId, userId);
            if (!note) {
                throw new Error('笔记不存在');
            }
            return note;
        } catch (error) {
            throw error;
        }
    }

    /**
     * 更新笔记
     * @param {string} noteId 笔记ID
     * @param {Object} updateData 更新数据
     * @param {string} userId 用户ID
     * @returns {Promise<Object>} 更新后的笔记
     */
    static async updateNote(noteId, updateData, userId) {
        try {
            // 检查笔记是否存在
            const existingNote = await NoteModel.findById(noteId, userId);
            if (!existingNote) {
                throw new Error('笔记不存在');
            }

            // 处理更新数据
            const updates = {};

            if (updateData.title !== undefined) {
                if (!updateData.title || updateData.title.trim().length === 0) {
                    throw new Error('笔记标题不能为空');
                }
                updates.title = updateData.title.trim();
            }

            if (updateData.content !== undefined) {
                updates.content = updateData.content ? updateData.content.trim() : '';
            }

            if (updateData.tags !== undefined) {
                // 处理标签：去重、去空、转小写
                updates.tags = [...new Set(updateData.tags
                    .map(tag => tag.trim().toLowerCase())
                    .filter(tag => tag.length > 0)
                )];
            }

            // 更新笔记
            const updatedNote = await NoteModel.update(noteId, updates, userId);
            return updatedNote;
        } catch (error) {
            throw error;
        }
    }

    /**
     * 删除笔记
     * @param {string} noteId 笔记ID
     * @param {string} userId 用户ID
     * @returns {Promise<void>}
     */
    static async deleteNote(noteId, userId) {
        try {
            // 检查笔记是否存在
            const existingNote = await NoteModel.findById(noteId, userId);
            if (!existingNote) {
                throw new Error('笔记不存在');
            }

            await NoteModel.delete(noteId, userId);
        } catch (error) {
            throw error;
        }
    }

    /**
     * 获取用户的所有标签统计
     * @param {string} userId 用户ID
     * @returns {Promise<Array>} 标签统计列表
     */
    static async getTagStats(userId) {
        try {
            return await NoteModel.getTags(userId);
        } catch (error) {
            throw error;
        }
    }

    /**
     * 批量删除笔记
     * @param {string[]} noteIds 笔记ID数组
     * @param {string} userId 用户ID
     * @returns {Promise<Object>} 删除结果
     */
    static async bulkDeleteNotes(noteIds, userId) {
        try {
            const results = {
                success: [],
                failed: []
            };

            for (const noteId of noteIds) {
                try {
                    await this.deleteNote(noteId, userId);
                    results.success.push(noteId);
                } catch (error) {
                    results.failed.push({
                        id: noteId,
                        error: error.message
                    });
                }
            }

            return results;
        } catch (error) {
            throw error;
        }
    }

    /**
     * 复制笔记
     * @param {string} noteId 要复制的笔记ID
     * @param {string} userId 用户ID
     * @returns {Promise<Object>} 新创建的笔记
     */
    static async duplicateNote(noteId, userId) {
        try {
            // 获取原笔记
            const originalNote = await NoteModel.findById(noteId, userId);
            if (!originalNote) {
                throw new Error('笔记不存在');
            }

            // 创建新笔记
            const newNote = await NoteModel.create({
                title: `${originalNote.title} (副本)`,
                content: originalNote.content,
                tags: originalNote.tags
            }, userId);

            return newNote;
        } catch (error) {
            throw error;
        }
    }
}

module.exports = NotesService; 