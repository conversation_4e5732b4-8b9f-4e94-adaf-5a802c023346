import { useEffect, useState } from 'react' // 第 1 行: 从 'react' 包导入 useEffect 和 useState Hooks。useEffect 用于处理副作用，useState 用于在函数组件中添加状态。

// 第 2 行: 空行，用于可读性。
interface UseIntersectionObserverProps { // 第 3 行: 定义一个 TypeScript 接口 UseIntersectionObserverProps，用于描述 Intersection Observer 的配置选项。
  threshold?: number // 第 4 行: threshold (可选属性): 一个数字或数字数组，表示目标元素可见性达到多少比例时触发回调。默认为 0。
  root?: Element | null // 第 5 行: root (可选属性): 指定根元素，用于检查目标元素的可见性。如果为 null 或未指定，则默认为浏览器视口。
  rootMargin?: string // 第 6 行: rootMargin (可选属性): 一个字符串，定义根元素的外边距，类似于 CSS margin 属性。例如 "10px 20px 30px 40px"。
} // 第 7 行: UseIntersectionObserverProps 接口定义的结束。

// 第 8 行: 空行。
export function useIntersectionObserver( // 第 9 行: 定义并导出一个名为 useIntersectionObserver 的自定义 React Hook。
  elementRef: React.RefObject<Element>, // 第 10 行: Hook 的第一个参数 elementRef，它是一个 React Ref 对象，指向要观察的 DOM 元素。
  { // 第 11 行: Hook 的第二个参数是一个对象，解构自 UseIntersectionObserverProps，并提供了默认值。
    threshold = 0, // 第 12 行: threshold 选项，如果未提供，则默认为 0。
    root = null, // 第 13 行: root 选项，如果未提供，则默认为 null (即浏览器视口)。
    rootMargin = '0%', // 第 14 行: rootMargin 选项，如果未提供，则默认为 '0%'。
  }: UseIntersectionObserverProps = {}, // 第 15 行: 为解构的配置对象指定类型 UseIntersectionObserverProps，并设置一个空对象作为其默认值，以防调用 Hook 时不传递第二个参数。
): IntersectionObserverEntry | undefined { // 第 16 行: 定义 Hook 的返回类型。它可以是一个 IntersectionObserverEntry 对象，或者在某些情况下是 undefined。
  const [entry, setEntry] = useState<IntersectionObserverEntry>() // 第 17 行: 使用 useState Hook 创建一个状态变量 entry 和其更新函数 setEntry。entry 用于存储最新的 IntersectionObserverEntry。

  // 第 18 行: 空行。
  useEffect(() => { // 第 19 行: 使用 useEffect Hook 来设置和清理 IntersectionObserver。
    const element = elementRef?.current // 第 20 行: 获取 elementRef 引用的当前 DOM 元素。使用可选链操作符 ?. 以防 elementRef 为空。
    if (!element) return // 第 21 行: 如果 DOM 元素不存在，则提前退出 effect，不执行任何操作。

    // 第 22 行: 空行。
    const observer = new IntersectionObserver( // 第 23 行: 创建一个新的 IntersectionObserver 实例。
      ([entry]) => { // 第 24 行: IntersectionObserver 的回调函数。当观察的元素与根元素的交叉状态改变时调用。参数是一个 IntersectionObserverEntry 对象数组，这里解构出第一个 entry。
        setEntry(entry) // 第 25 行: 调用 setEntry 更新状态，将最新的 IntersectionObserverEntry 存储起来。
      }, // 第 26 行: 回调函数的结束。
      { threshold, root, rootMargin } // 第 27 行: IntersectionObserver 的配置选项对象，使用从 Hook 参数中获取或默认的值。
    ) // 第 28 行: IntersectionObserver 构造函数调用的结束。

    // 第 29 行: 空行。
    observer.observe(element) // 第 30 行: 开始观察指定的 DOM 元素。

    // 第 31 行: 空行。
    return () => { // 第 32 行: useEffect 的清理函数。当组件卸载或依赖项改变导致 effect 重新运行时，此函数会被调用。
      observer.disconnect() // 第 33 行: 停止观察所有目标元素，释放资源。
    } // 第 34 行: 清理函数的结束。
  }, [elementRef, threshold, root, rootMargin]) // 第 35 行: useEffect 的依赖数组。当这些依赖项中的任何一个发生变化时，effect 都会重新运行（先执行清理函数，然后执行 effect 主体）。

  // 第 36 行: 空行。
  return entry // 第 37 行: 返回最新的 IntersectionObserverEntry 对象（或初始状态的 undefined）。
} // 第 38 行: useIntersectionObserver Hook 函数定义的结束。 