'use client'// 客户端组件

import { Suspense, useState, useEffect } from 'react' // 异步加载组件
import dynamic from 'next/dynamic' // 动态导入组件
import { Loader2, BookOpen, Sparkles, Plus } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { Note } from '@/types' // 笔记类型
import { getToken } from '@/lib/cookies' // 从 cookie 获取 token

// API基础URL
const API_BASE_URL = 'http://localhost:4000/api'

// API请求工具函数
async function fetchWithAuth(endpoint: string, options: RequestInit = {}) {
  const token = getToken() // 从 cookie 获取 token
  
  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
      ...options.headers,
    },
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.message || '请求失败')
  }

  return response.json()  //返回响应数据  可以 直接用  data.notes 等
}

// 懒加载组件  显示加载动画  直到组件就绪 减少 白屏时间
const NoteList = dynamic(
  () => import('@/components/notes/note-list').then(mod => mod.NoteList),
  {  //显示加载动画 直到组件就绪
    loading: () => (
      <div className="flex items-center justify-center h-32">
        <Loader2 className="w-6 h-6 animate-spin text-blue-500" />
      </div>
    ),
  }
)

const NoteEditor = dynamic(
  () => import('@/components/notes/note-editor').then(mod => mod.NoteEditor),
  {
    ssr: false,
    loading: () => (
      <div className="flex items-center justify-center h-96">
        <Loader2 className="w-8 h-8 animate-spin text-blue-500" />
      </div>
    ),
  }
)

export default function NotesPage() {
  const [selectedNote, setSelectedNote] = useState<Note | undefined>()
  const [notes, setNotes] = useState<Note[]>([])
  const [loading, setLoading] = useState(true)
  const { toast } = useToast()

  // 获取笔记列表
  useEffect(() => {
    const fetchNotes = async () => {
      try {
        setLoading(true)
        const response = await fetchWithAuth('/notes')
        setNotes(response.data.notes || [])
      } catch (err) {
        console.error('获取笔记列表失败:', err)
        toast({
          title: '❌ 加载失败',
          description: '获取笔记列表时出现错误',
          variant: 'destructive',
        })
      } finally {
        setLoading(false)
      }
    }

    fetchNotes()
  }, [toast]) // 
  // 当然这个 也可以 不写 依赖数组 但是 这样 会导致 每次 组件渲染 都会 重新 执行 这个 副作用 函数
  //如果是空数组 则 不会 重新 执行 这个 副作用 函数
  // ESlint 规则 要求 useEffect 内部使用的外部变量 都必须放在 依赖数组中

  // 创建笔记
  const handleCreateNote = async (note: Partial<Note>) => {
    try {
      const response = await fetchWithAuth('/notes', {
        method: 'POST',
        body: JSON.stringify({
          title: note.title,
          content: note.content,
          tags: note.tags || []
        }),
      })
      console.log("开始创建笔记:", note)
      console.log("API响应:", response)
      const newNote = response.data
      console.log("新笔记数据:", newNote)
      console.log("当前笔记列表:", notes)
      setNotes(prev => {
        console.log("更新后的笔记列表:", [...prev, newNote])
        return [...prev, newNote]
      })
      setSelectedNote(undefined)
      
      toast({
        title: '✨ 创建成功',
        description: '笔记已成功创建',
      })
    } catch (err) {
      console.error('创建笔记失败:', err)
      toast({
        title: '❌ 创建失败',
        description: err instanceof Error ? err.message : '创建笔记时出现错误',
        variant: 'destructive',
      })
    }
  }

  // 更新笔记
  const handleUpdateNote = async (note: Partial<Note>) => {
    if (!selectedNote?.id) return

    try {
      const updateData: any = {}
      if (note.title !== undefined) updateData.title = note.title
      if (note.content !== undefined) updateData.content = note.content
      if (note.tags !== undefined) updateData.tags = note.tags

      const response = await fetchWithAuth(`/notes/${selectedNote.id}`, {
        method: 'PUT',
        body: JSON.stringify(updateData),
      })
      
      const updatedNote = response.data.note
      setNotes(prev => prev.map(n => n.id === updatedNote.id ? updatedNote : n))
      setSelectedNote(updatedNote)
      
      toast({
        title: '✅ 更新成功',
        description: '笔记已成功更新',
      })
    } catch (err) {
      console.error('更新笔记失败:', err)
      toast({
        title: '❌ 更新失败',
        description: err instanceof Error ? err.message : '更新笔记时出现错误',
        variant: 'destructive',
      })
    }
  }

  // 删除笔记
  const handleDeleteNote = async (note: Note) => {
    try {
      await fetchWithAuth(`/notes/${note.id}`, {
        method: 'DELETE',
      })
      
      setNotes(prev => prev.filter(n => n.id !== note.id))
      if (selectedNote?.id === note.id) {
        setSelectedNote(undefined)
      }
      
      toast({
        title: '🗑️ 删除成功',
        description: '笔记已成功删除',
      })
    } catch (err) {
      console.error('删除笔记失败:', err)
      toast({
        title: '❌ 删除失败',
        description: err instanceof Error ? err.message : '删除笔记时出现错误',
        variant: 'destructive',
      })
    }
  }

  return (
    <div className="space-y-8">
      {/* 页面标题区域 */}
      <div className="text-center space-y-4">
        <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl shadow-lg mb-4">
          <BookOpen className="w-10 h-10 text-white" />
        </div>
        <div className="space-y-2">
          <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">
            我的笔记本
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            记录思考，整理知识，让每一个想法都有归宿
          </p>
        </div>
        
        {/* 装饰元素 */}
        <div className="flex items-center justify-center space-x-2 text-yellow-500">
          <Sparkles className="w-4 h-4 animate-pulse" />
          <span className="text-sm text-gray-500 dark:text-gray-400">开始你的创作之旅</span>
          <Sparkles className="w-4 h-4 animate-pulse" />
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* 笔记列表 */}
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-200 flex items-center gap-2">
              📚 笔记列表
            </h2>
          </div>
          
          <Suspense
            fallback={
              <div className="flex items-center justify-center h-32 bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50">
                <Loader2 className="w-6 h-6 animate-spin text-blue-500" />
              </div>
            }
          >
            <NoteList
              notes={notes}
              selectedNoteId={selectedNote?.id}
              onSelectNote={setSelectedNote}
              onDeleteNote={handleDeleteNote}
            />
          </Suspense>
        </div>

        {/* 笔记编辑器 */}
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-200 flex items-center gap-2">
              ✏️ {selectedNote ? '编辑笔记' : '创建笔记'}
            </h2>
            {!selectedNote && (
              <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                <Plus className="w-4 h-4" />
                <span>新建笔记</span>
              </div>
            )}
          </div>
          
          <Suspense
            fallback={
              <div className="flex items-center justify-center h-96 bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50">
                <Loader2 className="w-8 h-8 animate-spin text-blue-500" />
              </div>
            }
          >
            <NoteEditor
              note={selectedNote}
              onSave={selectedNote ? handleUpdateNote : handleCreateNote}
              onCancel={() => setSelectedNote(undefined)}
            />
          </Suspense>
        </div>
      </div>
    </div>
  )
} 