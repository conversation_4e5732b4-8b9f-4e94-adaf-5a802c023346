# 第 1 行: 这是一个注释行。提供了 GitHub 关于忽略文件的帮助文档链接。
# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# 第 2 行: 空行，用于分隔注释和规则。
# dependencies 第 3 行: 这是一个注释，标记以下规则是关于依赖项的。
/node_modules # 第 4 行: 忽略项目根目录下的 node_modules 文件夹。这是存放项目依赖的地方，通常非常大且可以从 package.json 重新安装。
/.pnp # 第 5 行: 忽略 .pnp 文件，与 Yarn PnP (Plug'n'Play) 相关。
.pnp.* # 第 6 行: 忽略所有以 .pnp. 开头的文件，例如 .pnp.cjs, .pnp.loader.mjs。
.yarn/* # 第 7 行: 忽略 .yarn 目录下的所有内容。这是 Yarn 2+ 存放其核心文件和缓存的地方。
!.yarn/patches # 第 8 行: 但不忽略 .yarn/patches 目录。感叹号 (!) 表示取消忽略模式。这允许提交 Yarn 的补丁文件。
!.yarn/plugins # 第 9 行: 不忽略 .yarn/plugins 目录，允许提交 Yarn 插件。
!.yarn/releases # 第 10 行: 不忽略 .yarn/releases 目录，允许提交 Yarn 的版本文件。
!.yarn/versions # 第 11 行: 不忽略 .yarn/versions 目录。

# 第 12 行: 空行。
# testing 第 13 行: 这是一个注释，标记以下规则是关于测试的。
/coverage # 第 14 行: 忽略项目根目录下的 coverage 文件夹。这个文件夹通常存放代码覆盖率报告，是测试过程的产物。

# 第 15 行: 空行。
# next.js 第 16 行: 这是一个注释，标记以下规则是关于 Next.js 特定文件的。
/.next/ # 第 17 行: 忽略项目根目录下的 .next 文件夹。这是 Next.js 的构建输出和缓存目录。
/out/ # 第 18 行: 忽略项目根目录下的 out 文件夹。这是运行 `next export` 时静态导出的输出目录。
 
# 第 19 行: 空行。
# production 第 20 行: 这是一个注释，标记以下规则是关于生产构建产物的 (尽管 Next.js 通常使用 .next)。
/build # 第 21 行: 忽略项目根目录下的 build 文件夹。一些项目的通用构建输出目录。

# 第 22 行: 空行。
# misc 第 23 行: 这是一个注释，标记以下规则是关于各种杂项文件的。
.DS_Store # 第 24 行: 忽略 macOS 系统自动生成的 .DS_Store 文件，用于存储文件夹的自定义属性。
*.pem # 第 25 行: 忽略所有以 .pem 结尾的文件。PEM 文件通常用于存放私钥或证书，不应提交到版本库。

# 第 26 行: 空行。
# debug 第 27 行: 这是一个注释，标记以下规则是关于调试日志文件的。
npm-debug.log* # 第 28 行: 忽略所有以 npm-debug.log 开头的文件 (例如 npm-debug.log.12345)。
yarn-debug.log* # 第 29 行: 忽略所有以 yarn-debug.log 开头的文件。
yarn-error.log* # 第 30 行: 忽略所有以 yarn-error.log 开头的文件。
.pnpm-debug.log* # 第 31 行: 忽略所有以 .pnpm-debug.log 开头的文件 (pnpm 包管理器的调试日志)。

# 第 32 行: 空行。
# env files (can opt-in for committing if needed) 第 33 行: 这是一个注释，标记以下规则是关于环境变量文件的。提示如果需要也可以选择提交它们（通常不推荐提交包含敏感信息的 .env 文件）。
.env* # 第 34 行: 忽略所有以 .env 开头的文件 (例如 .env, .env.local, .env.development)。这些文件通常包含环境变量和敏感凭据。

# 第 35 行: 空行。
# vercel 第 36 行: 这是一个注释，标记以下规则是关于 Vercel (一个流行的 Next.js 部署平台) 的。
.vercel # 第 37 行: 忽略 .vercel 文件夹，这是 Vercel CLI 用于本地开发和部署的配置目录。

# 第 38 行: 空行。
# typescript 第 39 行: 这是一个注释，标记以下规则是关于 TypeScript 特定文件的。
*.tsbuildinfo # 第 40 行: 忽略所有以 .tsbuildinfo 结尾的文件。这些文件由 TypeScript 的增量编译功能生成，用于存储上次编译的信息。
next-env.d.ts # 第 41 行: 忽略 next-env.d.ts 文件。这个文件是由 Next.js 自动生成的，用于确保 Next.js 的内置类型在 TypeScript 项目中可用。它不需要被版本控制，因为每次安装依赖或运行 Next.js 时都可能重新生成。

# 第 42 行: 文件末尾可能存在的空行或格式化。 