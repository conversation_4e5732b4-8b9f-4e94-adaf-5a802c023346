'use client'

import { useState, useEffect } from 'react'
import { useQuery } from '@tanstack/react-query'
import { getCurrentUser, User } from '@/lib/api/user'
import { getToken, removeToken, removeRefreshToken } from '@/lib/cookies'

export function useAuth() {
  const [isInitialized, setIsInitialized] = useState(false)
  const token = getToken()
  const isAuthenticated = !!token

  // 使用React Query获取用户信息
  const {
    data: user,
    error,
    isLoading,
    refetch: refetchUser
  } = useQuery({
    queryKey: ['user', 'me'],
    queryFn: getCurrentUser,
    enabled: isAuthenticated && isInitialized, // 只有在已认证且已初始化时才获取
    retry: (failureCount, error: any) => {
      // 如果是认证错误，不重试
      if (error?.response?.status === 401) {
        return false
      }
      return failureCount < 3
    },
    staleTime: 1000 * 60 * 5, // 5分钟内不重新获取
  })

  // 组件挂载后设置为已初始化
  useEffect(() => {
    setIsInitialized(true)
  }, [])

  // 如果获取用户信息失败且是认证错误，清除认证信息
  useEffect(() => {
    if (error && (error as any)?.response?.status === 401) {
      removeToken()
      removeRefreshToken()
      // 触发认证过期事件
      window.dispatchEvent(new CustomEvent('auth:expired', {
        detail: { message: '登录已过期，请重新登录1' }
      })) // window.addEventListener   添加 时间监听器，根据事件 做不同的动作
    }
  }, [error])

  const logout = () => {
    removeToken()
    removeRefreshToken()
    // 可以在这里调用API的logout接口
    window.location.href = '/login'
  }

  return {
    user: user || null,
    isAuthenticated,
    isLoading: isLoading && isAuthenticated,
    error,
    logout,
    refetchUser,
    isInitialized
  }
} 