---
description:
globs:
alwaysApply: false
---
# Git工作流程指南

## 分支命名

- `main`: 主分支，生产环境代码
- `develop`: 开发分支
- `feature/*`: 新功能分支
- `bugfix/*`: 错误修复分支
- `hotfix/*`: 紧急修复分支

## 提交信息格式

使用以下格式:
```
<type>(<scope>): <subject>

<body>

<footer>
```

类型（type）:
- `feat`: 新功能
- `fix`: 错误修复
- `docs`: 文档更改
- `style`: 代码格式修改
- `refactor`: 代码重构
- `test`: 测试用例修改
- `chore`: 构建过程或辅助工具的变动

示例:
```
feat(auth): 添加用户登录功能

- 实现用户名密码登录
- 添加JWT token处理
- 集成Supabase认证

Closes #123
```

## 工作流程

1. 从`develop`分支创建新的功能分支
2. 完成开发后提交Pull Request
3. 代码审查通过后合并到`develop`
4. 定期将`develop`合并到`main`进行发布

## 发布流程

1. 在`develop`分支完成功能开发和测试
2. 创建发布分支`release/v*.*.*`
3. 进行最终测试和bug修复
4. 合并到`main`分支并打tag
5. 将改动同步回`develop`分支
