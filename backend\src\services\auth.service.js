const UserModel = require('../models/user.model');
const { verifyPassword, generateToken } = require('../utils/auth');

class AuthService {
    /**
     * 用户注册
     * @param {Object} userData 用户注册数据
     * @returns {Promise<Object>} 注册成功的用户信息和token
     */
    static async register(userData) { 
        try {
            // 创建用户
            const user = await UserModel.create(userData);

            // 生成token
            const token = generateToken({ id: user.id });

            // 返回用户信息（不包含密码）和token
            const { password_hash, ...userWithoutPassword } = user;
            return {
                user: userWithoutPassword,
                token
            };
        } catch (error) {
            throw error;
        }
    }

    /**
     * 用户登录
     * @param {string} email 邮箱
     * @param {string} password 密码
     * @returns {Promise<Object>} 登录成功的用户信息和token
     */
    static async login(email, password) {
        try {
            // 查找用户
            const user = await UserModel.findByEmail(email);
            if (!user) {
                throw new Error('用户不存在');
            }

            // 验证密码
            const isValidPassword = await verifyPassword(password, user.password_hash);
            if (!isValidPassword) {
                throw new Error('密码错误');
            }

            // 生成token
            const token = generateToken({ id: user.id });

            // 返回用户信息（不包含密码）和token
            const { password_hash, ...userWithoutPassword } = user;
            return {
                user: userWithoutPassword,
                token
            };
        } catch (error) {
            throw error;
        }
    }

    /**
     * 获取当前用户信息
     * @param {string} userId 用户ID
     * @returns {Promise<Object>} 用户信息
     */
    static async getCurrentUser(userId) {
        try {
            const user = await UserModel.findById(userId);
            if (!user) {
                throw new Error('用户不存在');
            }

            // 返回用户信息（不包含密码）
            const { password_hash, ...userWithoutPassword } = user;
            return userWithoutPassword;
        } catch (error) {
            throw error;
        }
    }
}

module.exports = AuthService; 