import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // 实验性功能配置
  experimental: {
    // 启用优化的字体加载
    optimizePackageImports: ['lucide-react'],
  },

  // 字体优化配置
  optimizeFonts: true,

  // 如果需要使用 Turbopack，可以在这里配置
  // turbo: {
  //   rules: {
  //     '*.svg': {
  //       loaders: ['@svgr/webpack'],
  //       as: '*.js',
  //     },
  //   },
  // },
};

export default nextConfig;
