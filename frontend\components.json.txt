{
  // "$schema": "https://ui.shadcn.com/schema.json", 
  // 定义了此JSON文件的结构遵循的schema，主要用于编辑器的自动完成和验证。

  // "style": "new-york",
  // 指定了UI组件的视觉风格。shadcn/ui 提供不同的预设风格，如 "default" 和 "new-york"。
  // "new-york" 风格通常更紧凑和精致。

  // "rsc": true,
  // 表示项目是否使用 React Server Components (RSC)。
  // true 表示组件会优先被生成为与RSC兼容的代码。

  // "tsx": true,
  // 表示项目使用 TypeScript 和 JSX (.tsx 文件)。
  // 如果是false，可能会生成.jsx文件。

  "tailwind": {
    // "config": "tailwind.config.ts",
    // 指定 Tailwind CSS 配置文件的路径。shadcn/ui CLI 会读取此文件以了解你的Tailwind设置。

    // "css": "src/app/globals.css",
    // 指定全局CSS文件的路径。shadcn/ui 可能会在此文件中注入一些基础样式或CSS变量。

    // "baseColor": "neutral",
    // 定义了用于生成组件颜色主题的基础色调。例如 "neutral", "slate", "zinc" 等。
    // 这会影响组件的默认颜色。

    // "cssVariables": true,
    // 表示是否使用CSS自定义属性（CSS变量）来定义颜色和其他Tailwind主题值。
    // true 意味着颜色等会通过变量如 `--primary`, `--secondary` 来定义，方便动态切换主题。

    // "prefix": ""
    // 为 Tailwind CSS 工具类添加前缀。空字符串表示不使用前缀 (例如 `text-red-500`)。
    // 如果设置为 "tw-"，则类名会变成 `tw-text-red-500`。
  },

  "aliases": {
    // "components": "@/components",
    // 定义了导入组件时使用的路径别名。`@/components` 会被解析为实际的 `components` 目录路径。
    // 这是为了简化导入语句，使其更易读和维护。

    // "utils": "@/lib/utils",
    // 定义了导入工具函数/模块时使用的路径别名，指向 `lib/utils` 目录。

    // "ui": "@/components/ui",
    // 定义了导入 shadcn/ui 生成的UI组件时使用的路径别名，指向 `components/ui` 目录。
    // 当你使用 `npx shadcn-ui add <component>` 时，组件会被放置在这个目录下。

    // "lib": "@/lib",
    // 定义了指向 `lib` 目录的路径别名，通常用于存放辅助函数、工具等。

    // "hooks": "@/hooks"
    // 定义了指向 `hooks` 目录的路径别名，用于存放自定义React Hooks。
  },

  // "iconLibrary": "lucide"
  // 指定了项目使用的图标库。这里设置为 "lucide"，表示使用 lucide-react 图标库。
  // shadcn/ui CLI 在添加需要图标的组件时，会使用此库的图标。
} 