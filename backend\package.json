{"name": "notes-backend", "version": "1.0.0", "description": "Notes API Backend", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@supabase/supabase-js": "^2.39.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-validator": "^7.2.1", "helmet": "^8.1.0", "morgan": "^1.10.0"}, "devDependencies": {"debug": "^4.4.1", "nodemon": "^3.0.2"}}