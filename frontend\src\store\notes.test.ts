import { useNotesStore } from './notes';
import { Note } from '@/types';

// 定义真正的初始状态，确保每次重置都是全新的对象
// 类型直接在此处定义，因为 NotesState 未从外部导出
const initialNotesState: {
  selectedNote: Note | null;
  searchQuery: string;
  selectedTags: string[];
} = {
  selectedNote: null,
  searchQuery: '',
  selectedTags: [],
};

const mockNote: Note = {
  id: 'note1',
  title: 'Test Note',
  content: 'This is a test note.',
  tags: ['test', 'sample'],
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  userId: 'user1',
};

describe('useNotesStore', () => {
  beforeEach(() => {
    // 重置 store 的数据字段到定义的初始状态，保留 action
    useNotesStore.setState(initialNotesState);
  });

  it('应该有正确的初始状态', () => {
    const state = useNotesStore.getState();
    expect(state.selectedNote).toBeNull();
    expect(state.searchQuery).toBe('');
    expect(state.selectedTags).toEqual([]);
  });

  describe('setSelectedNote', () => {
    it('应该设置 selectedNote', () => {
      useNotesStore.getState().setSelectedNote(mockNote);
      expect(useNotesStore.getState().selectedNote).toEqual(mockNote);
    });

    it('设置 selectedNote 为 null 时应该清除它', () => {
      useNotesStore.getState().setSelectedNote(mockNote); // 先设置
      useNotesStore.getState().setSelectedNote(null); // 再清除
      expect(useNotesStore.getState().selectedNote).toBeNull();
    });
  });

  describe('setSearchQuery', () => {
    it('应该更新 searchQuery', () => {
      const query = 'test query';
      useNotesStore.getState().setSearchQuery(query);
      expect(useNotesStore.getState().searchQuery).toBe(query);
    });
  });

  describe('setSelectedTags', () => {
    it('应该直接设置 selectedTags 数组', () => {
      const tags = ['tag1', 'tag2'];
      useNotesStore.getState().setSelectedTags(tags);
      expect(useNotesStore.getState().selectedTags).toEqual(tags);
    });
  });

  describe('addSelectedTag', () => {
    it('如果标签不存在，则添加到 selectedTags', () => {
      useNotesStore.getState().addSelectedTag('tag1');
      expect(useNotesStore.getState().selectedTags).toEqual(['tag1']);
      useNotesStore.getState().addSelectedTag('tag2');
      expect(useNotesStore.getState().selectedTags).toEqual(['tag1', 'tag2']);
    });

    it('如果标签已存在，则不应重复添加', () => {
      useNotesStore.getState().addSelectedTag('tag1');
      useNotesStore.getState().addSelectedTag('tag1'); // 再次添加相同的标签
      expect(useNotesStore.getState().selectedTags).toEqual(['tag1']);
    });
  });

  describe('removeSelectedTag', () => {
    it('应该从 selectedTags 中移除指定的标签', () => {
      useNotesStore.getState().setSelectedTags(['tag1', 'tag2', 'tag3']);
      useNotesStore.getState().removeSelectedTag('tag2');
      expect(useNotesStore.getState().selectedTags).toEqual(['tag1', 'tag3']);
    });

    it('如果标签不存在于 selectedTags 中，则不执行任何操作', () => {
      useNotesStore.getState().setSelectedTags(['tag1', 'tag3']);
      useNotesStore.getState().removeSelectedTag('tag2');
      expect(useNotesStore.getState().selectedTags).toEqual(['tag1', 'tag3']);
    });
  });

  describe('clearFilters', () => {
    it('应该重置 searchQuery 和 selectedTags', () => {
      useNotesStore.getState().setSearchQuery('some query');
      useNotesStore.getState().setSelectedTags(['tag1', 'tag2']);
      useNotesStore.getState().clearFilters();
      const state = useNotesStore.getState();
      expect(state.searchQuery).toBe('');
      expect(state.selectedTags).toEqual([]);
    });
  });

  describe('clearSelectedTags', () => {
    it('应该只重置 selectedTags，不影响 searchQuery', () => {
      useNotesStore.getState().setSearchQuery('persistent query');
      useNotesStore.getState().setSelectedTags(['tagA', 'tagB']);
      useNotesStore.getState().clearSelectedTags();
      const state = useNotesStore.getState();
      expect(state.searchQuery).toBe('persistent query');
      expect(state.selectedTags).toEqual([]);
    });
  });
}); 