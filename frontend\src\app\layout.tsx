import type { Metadata } from "next";
import { Inter } from "next/font/google";
import { Providers } from "@/components/providers";
import { Toaster } from "@/components/ui/toaster";
import { ErrorBoundary } from "@/components/error-boundary";
import { AnalyticsProvider } from "@/lib/analytics";
import { StagewiseToolbar } from "@stagewise/toolbar-next";
import { ReactPlugin } from "@stagewise-plugins/react";
import "./globals.css";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "笔记应用",
  description: "一个简单的笔记应用",
  viewport: "width=device-width, initial-scale=1",
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "white" },
    { media: "(prefers-color-scheme: dark)", color: "black" },
  ],
};

// 性能指标收集
export function reportWebVitals(metric: any) {
  if (process.env.NODE_ENV === 'production') {
    // 在生产环境中收集性能指标
    console.log(metric);
  }
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN" suppressHydrationWarning>
      <head>
        <link
          rel="preload"
          href="/fonts/inter.woff2"
          as="font"
          type="font/woff2"
          crossOrigin="anonymous"
        />
        <link
          rel="preconnect"
          href={process.env.NEXT_PUBLIC_API_URL}
          crossOrigin="anonymous"
        />
      </head>
      <body className={inter.className}>
        <ErrorBoundary>
          <Providers>
            <AnalyticsProvider>
              {children}
              <Toaster />
              <StagewiseToolbar 
                config={{
                  plugins: [ReactPlugin],
                }}
              />
            </AnalyticsProvider>
          </Providers>
        </ErrorBoundary>
      </body>
    </html>
  );
}
