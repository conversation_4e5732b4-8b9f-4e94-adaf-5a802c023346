const express = require('express');
const { createClient } = require('@supabase/supabase-js');
const router = express.Router();

// 创建 Supabase 客户端
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// 注册路由
router.post('/register', async (req, res) => {
    try {
        const { email, password, name } = req.body;

        if (!email || !password) {
            return res.status(400).json({
                status: 'error',
                message: '邮箱和密码不能为空'
            });
        }

        // 1. 创建认证用户
        const { data: authData, error: authError } = await supabase.auth.signUp({
            email,
            password,
            options: {
                data: { name },
                emailRedirectTo: null,
                // 禁用邮箱验证
                gotrue: {
                    email_confirmed: true,
                    skip_confirm: true
                }
            }
        });

        if (authError) {
            console.error('认证注册失败:', authError);
            return res.status(400).json({
                status: 'error',
                message: authError.message
            });
        }

        if (!authData.user) {
            return res.status(400).json({
                status: 'error',
                message: '用户创建失败'
            });
        }

        // 2. 在 users 表中创建用户记录
        const { data: userData, error: userError } = await supabase
            .from('users')
            .insert([
                {
                    id: authData.user.id,
                    email: email,
                    name: name
                }
            ])
            .select()
            .single();

        if (userError) {
            console.error('用户表创建失败:', userError);
            return res.status(500).json({
                status: 'error',
                message: '用户创建失败'
            });
        }

        // 3. 返回成功响应
        res.status(201).json({
            status: 'success',
            data: {
                user: userData,
                session: authData.session,
                message: '注册成功'
            }
        });
    } catch (error) {
        console.error('注册过程出错:', error);
        res.status(500).json({
            status: 'error',
            message: '服务器内部错误'
        });
    }
});

// 登录路由
router.post('/login', async (req, res) => {
    try {
        const { email, password } = req.body;

        if (!email || !password) {
            return res.status(400).json({
                status: 'error',
                message: '邮箱和密码不能为空'
            });
        }

        const { data, error } = await supabase.auth.signInWithPassword({
            email,
            password
        });

        if (error) {
            return res.status(401).json({
                status: 'error',
                message: '邮箱或密码错误'
            });
        }

        // 获取用户详细信息
        const { data: userData, error: userError } = await supabase
            .from('users')
            .select('*')
            .eq('id', data.user.id)
            .single();

        if (userError) {
            return res.status(500).json({
                status: 'error',
                message: '获取用户信息失败'
            });
        }

        res.json({
            status: 'success',
            data: {
                user: userData,
                session: data.session
            }
        });
    } catch (error) {
        res.status(500).json({
            status: 'error',
            message: error.message
        });
    }
});

// 获取当前用户信息
router.get('/me', async (req, res) => {
    try {
        // 从请求头获取token
        const authHeader = req.headers.authorization;
        if (!authHeader) {
            return res.status(401).json({
                status: 'error',
                message: '未提供认证令牌'
            });
        }

        const token = authHeader.split(' ')[1];
        if (!token) {
            return res.status(401).json({
                status: 'error',
                message: '认证令牌格式错误'
            });
        }

        // 验证token并获取用户
        const { data: { user }, error } = await supabase.auth.getUser(token);

        if (error || !user) {
            return res.status(401).json({
                status: 'error',
                message: '无效的认证令牌'
            });
        }

        // 从users表获取用户详细信息，保持与登录/注册接口一致的数据格式
        const { data: userData, error: userError } = await supabase
            .from('users')
            .select('*')
            .eq('id', user.id)
            .single();

        if (userError) {
            return res.status(500).json({
                status: 'error',
                message: '获取用户信息失败'
            });
        }

        res.json({
            status: 'success',
            data: { user: userData }
        });
    } catch (error) {
        res.status(500).json({
            status: 'error',
            message: error.message
        });
    }
});

module.exports = router; 