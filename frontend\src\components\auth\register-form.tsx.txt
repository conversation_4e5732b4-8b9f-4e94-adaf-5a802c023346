'use client' // 第 1 行: Next.js 指令，将此文件标记为客户端组件。

// 第 2 行: 空行，用于可读性。
import { useState } from 'react' // 第 3 行: 从 'react' 导入 useState Hook，用于管理组件的加载状态。
import { useRouter } from 'next/navigation' // 第 4 行: 从 'next/navigation' 导入 useRouter Hook，用于编程式导航。
import { useForm } from 'react-hook-form' // 第 5 行: 从 'react-hook-form' 导入 useForm Hook，用于表单管理和验证。
import { zodResolver } from '@hookform/resolvers/zod' // 第 6 行: 从 '@hookform/resolvers/zod' 导入 zodResolver，用于连接 zod 验证和 react-hook-form。
import * as z from 'zod' // 第 7 行: 从 'zod' 包导入所有导出内容并命名为 z。Zod 是一个 TypeScript 优先的数据验证库。

/* 
=== import * as z from 'zod' 详细解释 ===

1. 什么是 Zod？
   - Zod 是一个 TypeScript 优先的数据验证库
   - 用于表单验证、API 数据验证、类型安全
   - 在运行时验证数据，同时提供 TypeScript 类型支持

2. 导入语法解释：
   - import * : 导入整个模块的所有导出内容
   - as z : 给导入的模块起别名叫 z（zod 官方推荐的使用方式）
   - from 'zod' : 从 zod 这个 npm 包导入

3. 为什么不写 import { ... } from 'zod'？
   - zod 有很多验证方法，使用 * as z 可以访问所有方法
   - 代码更简洁，不需要列出每个单独的导入
   - 方便使用：z.string(), z.number(), z.object() 等

4. 常见的 Zod 验证方法：
   - z.string()           // 字符串
   - z.number()           // 数字  
   - z.boolean()          // 布尔值
   - z.date()             // 日期
   - z.string().min(2)    // 最少2个字符
   - z.string().email()   // 邮箱格式
   - z.string().url()     // URL格式
   - z.number().min(0)    // 最小值0
   - z.array(z.string())  // 字符串数组
   - z.object({...})      // 对象验证
   - z.string().optional() // 可选字段
*/

import { Button } from '@/components/ui/button' // 第 8 行: 从项目路径导入 Button UI 组件。
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form' // 第 9-16 行: 从项目路径导入表单相关的 UI 组件。
import { Input } from '@/components/ui/input' // 第 17 行: 从项目路径导入 Input UI 组件。
import { setToken, setRefreshToken } from '@/lib/cookies' // 第 18 行: 从项目路径导入 token 管理函数。

// 第 19 行: 定义表单验证规则使用 zod
const formSchema = z.object({ // z.object({}) 定义一个对象的验证规则
  name: z.string().min(2, { // name 字段：必须是字符串，最少2个字符
    message: '名字至少需要2个字符', // 验证失败时的错误信息
  }),
  email: z.string().email({ // email 字段：必须是字符串且符合邮箱格式
    message: '请输入有效的邮箱地址', // 验证失败时的错误信息
  }),
  password: z.string().min(6, { // password 字段：必须是字符串，最少6个字符
    message: '密码至少需要6个字符', // 验证失败时的错误信息
  }),
  confirmPassword: z.string(), // confirmPassword 字段：必须是字符串
}).refine((data) => data.password === data.confirmPassword, { // 自定义验证规则：检查密码和确认密码是否一致
  message: '两次输入的密码不一致', // 验证失败时的错误信息
  path: ['confirmPassword'], // 错误信息显示在 confirmPassword 字段上
})

/* 
=== formSchema 详细解释 ===

1. z.object({}) 的作用：
   - 定义一个对象的验证结构
   - 每个属性都有自己的验证规则

2. 验证规则说明：
   - z.string().min(2) : 字符串类型，最少2个字符
   - z.string().email() : 字符串类型，必须符合邮箱格式
   - message 属性 : 自定义错误信息

3. .refine() 的作用：
   - 添加自定义验证逻辑
   - 可以跨字段验证（如密码确认）
   - path 指定错误信息显示在哪个字段

4. 自动生成的 TypeScript 类型：
   type FormData = {
     name: string;
     email: string;
     password: string;
     confirmPassword: string;
   }
*/

export function RegisterForm() { // 第 34 行: 定义并导出 RegisterForm 组件。
  const router = useRouter() // 第 35 行: 获取路由器实例，用于页面跳转。
  const [isLoading, setIsLoading] = useState(false) // 第 36 行: 定义加载状态，用于显示提交时的加载效果。

  const form = useForm<z.infer<typeof formSchema>>({ // 第 38 行: 使用 useForm Hook 创建表单实例
    // z.infer<typeof formSchema> : 从 zod schema 自动生成 TypeScript 类型
    resolver: zodResolver(formSchema), // zodResolver : 连接 zod 验证和 react-hook-form
    defaultValues: { // 设置表单字段的默认值
      name: '',
      email: '',
      password: '',
      confirmPassword: '',
    },
  })

  /* 
  === z.infer<typeof formSchema> 详细解释 ===
  
  1. z.infer 的作用：
     - 从 zod schema 自动推断 TypeScript 类型
     - 确保表单数据类型与验证规则一致
     
  2. typeof formSchema：
     - 获取 formSchema 的类型
     - 然后通过 z.infer 推断出数据类型
     
  3. 等价于手写类型：
     type FormData = {
       name: string;
       email: string;
       password: string; 
       confirmPassword: string;
     }
  
  4. 优势：
     - 类型安全：编译时检查类型错误
     - 自动同步：schema 改变时类型自动更新
     - 减少重复：不需要重复定义类型
  */

  async function onSubmit(values: z.infer<typeof formSchema>) { // 第 48 行: 定义表单提交处理函数
    // values 参数的类型也是从 zod schema 推断出来的
    setIsLoading(true) // 设置加载状态为 true

    try {
      const response = await fetch('/api/auth/register', { // 向注册 API 发送请求
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(values), // values 已经通过 zod 验证，确保数据格式正确
      })

      const data = await response.json()

      if (response.ok) {
        setToken(data.token) // 保存访问 token
        setRefreshToken(data.refreshToken) // 保存刷新 token
        router.push('/dashboard') // 跳转到仪表盘
      } else {
        throw new Error(data.message || '注册失败')
      }
    } catch (error) {
      console.error('注册错误:', error)
      // 这里可以添加错误提示，比如使用 toast
    } finally {
      setIsLoading(false) // 无论成功失败都要重置加载状态
    }
  }

  /* 
  === Zod 验证工作流程 ===
  
  1. 用户输入数据：
     const userInput = {
       name: "张三",
       email: "<EMAIL>",
       password: "123456", 
       confirmPassword: "123456"
     }
  
  2. Zod 自动验证：
     - react-hook-form 使用 zodResolver 自动调用验证
     - 如果验证失败，显示错误信息
     - 如果验证成功，调用 onSubmit 函数
  
  3. 验证结果：
     成功：{ success: true, data: validatedData }
     失败：{ success: false, error: { errors: [...] } }
  
  4. 类型安全：
     - onSubmit 函数的 values 参数已经是验证过的数据
     - TypeScript 确保数据类型正确
  */

  return (
    <Form {...form}> {/* 使用 shadcn/ui 的 Form 组件，传入 form 实例 */}
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8"> {/* 表单提交时会先进行 zod 验证 */}
        <FormField
          control={form.control}
          name="name" {/* 对应 formSchema 中的 name 字段 */}
          render={({ field }) => (
            <FormItem>
              <FormLabel>名字</FormLabel>
              <FormControl>
                <Input placeholder="你的名字" {...field} />
              </FormControl>
              <FormMessage /> {/* 自动显示 zod 验证的错误信息 */}
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="email" {/* 对应 formSchema 中的 email 字段 */}
          render={({ field }) => (
            <FormItem>
              <FormLabel>邮箱</FormLabel>
              <FormControl>
                <Input placeholder="<EMAIL>" {...field} />
              </FormControl>
              <FormMessage /> {/* 如果邮箱格式不正确，这里会显示："请输入有效的邮箱地址" */}
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="password" {/* 对应 formSchema 中的 password 字段 */}
          render={({ field }) => (
            <FormItem>
              <FormLabel>密码</FormLabel>
              <FormControl>
                <Input type="password" {...field} />
              </FormControl>
              <FormMessage /> {/* 如果密码少于6个字符，这里会显示："密码至少需要6个字符" */}
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="confirmPassword" {/* 对应 formSchema 中的 confirmPassword 字段 */}
          render={({ field }) => (
            <FormItem>
              <FormLabel>确认密码</FormLabel>
              <FormControl>
                <Input type="password" {...field} />
              </FormControl>
              <FormMessage /> {/* 如果两次密码不一致，这里会显示："两次输入的密码不一致" */}
            </FormItem>
          )}
        />
        <Button type="submit" disabled={isLoading}>
          {isLoading ? '注册中...' : '注册'} {/* 根据加载状态显示不同文本 */}
        </Button>
      </form>
    </Form>
  )
}

/* 
=== 使用 Zod 的优势总结 ===

1. 类型安全：
   - 编译时和运行时都有类型检查
   - 自动生成 TypeScript 类型
   - 避免类型不匹配的错误

2. 简洁易读：
   - 声明式的验证规则
   - 自定义错误信息
   - 链式调用，代码简洁

3. 功能强大：
   - 支持复杂的验证逻辑
   - 跨字段验证（refine）
   - 数组、对象、嵌套验证

4. 生态系统：
   - 与 react-hook-form 完美集成
   - 与 Next.js、TypeScript 天然配合
   - 社区活跃，文档完善

5. 对比手动验证：
   
   手动验证（繁琐且容易出错）：
   function validateEmail(email) {
     if (!email) return '邮箱不能为空'
     if (!/\S+@\S+\.\S+/.test(email)) return '邮箱格式不正确'
     return null
   }
   
   Zod 验证（简洁且类型安全）：
   email: z.string().email({ message: '请输入有效的邮箱地址' })

6. 实际项目价值：
   - 减少 bug：验证逻辑集中管理
   - 提高效率：自动处理表单验证
   - 改善体验：实时显示验证错误
   - 便于维护：验证规则清晰明确
*/ 