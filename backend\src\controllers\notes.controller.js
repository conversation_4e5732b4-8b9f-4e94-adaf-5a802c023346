const NotesService = require('../services/notes.service');
const { validationResult } = require('express-validator');

class NotesController {
    /**
     * 创建笔记
     * @param {Object} req 请求对象
     * @param {Object} res 响应对象
     */
    static async createNote(req, res) {
        try {
            // 验证请求数据
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    status: 'error',
                    errors: errors.array()
                });
            }

            const noteData = req.body;
            const userId = req.user.id;

            const note = await NotesService.createNote(noteData, userId);

            res.status(201).json({
                status: 'success',
                data: { note }
            });
        } catch (error) {
            res.status(400).json({
                status: 'error',
                message: error.message
            });
        }
    }

    /**
     * 获取笔记列表
     * @param {Object} req 请求对象
     * @param {Object} res 响应对象
     */
    static async getNotes(req, res) {
        try {
            const userId = req.user.id;
            const { page, limit, tag, search, sortBy, sortOrder } = req.query;

            const result = await NotesService.getNotes(userId, {
                page,
                limit,
                tag,
                search,
                sortBy,
                sortOrder
            });

            res.json({
                status: 'success',
                data: result
            });
        } catch (error) {
            res.status(400).json({
                status: 'error',
                message: error.message
            });
        }
    }

    /**
     * 获取单个笔记
     * @param {Object} req 请求对象
     * @param {Object} res 响应对象
     */
    static async getNoteById(req, res) {
        try {
            const { id } = req.params;
            const userId = req.user.id;

            const note = await NotesService.getNoteById(id, userId);

            res.json({
                status: 'success',
                data: { note }
            });
        } catch (error) {
            res.status(error.message === '笔记不存在' ? 404 : 400).json({
                status: 'error',
                message: error.message
            });
        }
    }

    /**
     * 更新笔记
     * @param {Object} req 请求对象
     * @param {Object} res 响应对象
     */
    static async updateNote(req, res) {
        try {
            // 验证请求数据
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    status: 'error',
                    errors: errors.array()
                });
            }

            const { id } = req.params;
            const updateData = req.body;
            const userId = req.user.id;

            const note = await NotesService.updateNote(id, updateData, userId);

            res.json({
                status: 'success',
                data: { note }
            });
        } catch (error) {
            res.status(error.message === '笔记不存在' ? 404 : 400).json({
                status: 'error',
                message: error.message
            });
        }
    }

    /**
     * 删除笔记
     * @param {Object} req 请求对象
     * @param {Object} res 响应对象
     */
    static async deleteNote(req, res) {
        try {
            const { id } = req.params;
            const userId = req.user.id;

            await NotesService.deleteNote(id, userId);

            res.json({
                status: 'success',
                data: {
                    message: '笔记已成功删除'
                }
            });
        } catch (error) {
            res.status(error.message === '笔记不存在' ? 404 : 400).json({
                status: 'error',
                message: error.message
            });
        }
    }

    /**
     * 获取标签统计
     * @param {Object} req 请求对象
     * @param {Object} res 响应对象
     */
    static async getTagStats(req, res) {
        try {
            const userId = req.user.id;
            const tags = await NotesService.getTagStats(userId);

            res.json({
                status: 'success',
                data: { tags }
            });
        } catch (error) {
            res.status(400).json({
                status: 'error',
                message: error.message
            });
        }
    }

    /**
     * 批量删除笔记
     * @param {Object} req 请求对象
     * @param {Object} res 响应对象
     */
    static async bulkDeleteNotes(req, res) {
        try {
            const { noteIds } = req.body;
            const userId = req.user.id;

            if (!Array.isArray(noteIds) || noteIds.length === 0) {
                return res.status(400).json({
                    status: 'error',
                    message: '请提供有效的笔记ID数组'
                });
            }

            const result = await NotesService.bulkDeleteNotes(noteIds, userId);

            res.json({
                status: 'success',
                data: result
            });
        } catch (error) {
            res.status(400).json({
                status: 'error',
                message: error.message
            });
        }
    }

    /**
     * 复制笔记
     * @param {Object} req 请求对象
     * @param {Object} res 响应对象
     */
    static async duplicateNote(req, res) {
        try {
            const { id } = req.params;
            const userId = req.user.id;

            const note = await NotesService.duplicateNote(id, userId);

            res.status(201).json({
                status: 'success',
                data: { note }
            });
        } catch (error) {
            res.status(error.message === '笔记不存在' ? 404 : 400).json({
                status: 'error',
                message: error.message
            });
        }
    }
}

module.exports = NotesController; 