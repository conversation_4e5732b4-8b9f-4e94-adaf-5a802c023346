import { useState, KeyboardEvent } from 'react'
import { X } from 'lucide-react'
import { Badge } from './badge'
import { Input } from './input'

interface TagInputProps {
  tags: string[]
  onChange: (tags: string[]) => void
  placeholder?: string
  maxTags?: number
}

export function TagInput({
  tags,
  onChange,
  placeholder = '输入标签...',
  maxTags = 10,
}: TagInputProps) {
  const [inputValue, setInputValue] = useState('')

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault()
      const newTag = inputValue.trim()
      
      if (newTag && !tags.includes(newTag) && tags.length < maxTags) {
        onChange([...tags, newTag])
        setInputValue('')
      }
    } else if (e.key === 'Backspace' && !inputValue && tags.length > 0) {
      onChange(tags.slice(0, -1))
    }
  }

  const removeTag = (tagToRemove: string) => {
    onChange(tags.filter(tag => tag !== tagToRemove))
  }

  return (
    <div className="flex flex-wrap gap-2 p-2 border rounded-md bg-background">
      {tags.map(tag => (
        <Badge key={tag} variant="secondary" className="gap-1">
          {tag}
          <button
            type="button"
            onClick={() => removeTag(tag)}
            className="ml-1 rounded-full outline-none focus:ring-2 focus:ring-ring"
          >
            <X className="h-3 w-3" />
          </button>
        </Badge>
      ))}
      <Input
        type="text"
        value={inputValue}
        onChange={(e) => setInputValue(e.target.value)}
        onKeyDown={handleKeyDown}
        placeholder={tags.length < maxTags ? placeholder : `已达到最大标签数量 (${maxTags})`}
        disabled={tags.length >= maxTags}
        className="flex-1 border-0 focus-visible:ring-0 focus-visible:ring-offset-0 px-0"
      />
    </div>
  )
} 