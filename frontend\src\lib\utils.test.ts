import { cn, formatDate, debounce, truncateText } from './utils';

describe('cn', () => {
  it('应该正确合并和去重 Tailwind 类', () => {
    expect(cn('px-2 py-1 bg-red-500', 'p-4 bg-blue-500')).toBe('p-4 bg-blue-500');
    expect(cn('text-left', 'text-center', 'text-right')).toBe('text-right');
    expect(cn('font-bold', null, 'text-lg', undefined, { 'p-2': true, 'm-4': false })).toBe(
      'font-bold text-lg p-2'
    );
    expect(cn({ 'flex items-center': true }, 'justify-center')).toBe(
      'flex items-center justify-center'
    );
  });

  it('应该处理不同类型的输入', () => {
    expect(cn('foo', 'bar')).toBe('foo bar');
    expect(cn('foo', { bar: true, duck: false })).toBe('foo bar');
    expect(cn({ 'foo-bar': true })).toBe('foo-bar');
    expect(cn({ foo: true }, { bar: true })).toBe('foo bar');
    expect(cn({ foo: true, bar: false, baz: true })).toBe('foo baz');
    expect(cn('foo', ['bar', { baz: true }])).toBe('foo bar baz');
    expect(cn(['foo', ['bar', ['baz']]], 'qux')).toBe('foo bar baz qux');
  });
});

describe('formatDate', () => {
  it('应该正确格式化有效的日期字符串', () => {
    // 注意: toLocaleDateString 的输出可能因 NodeJS 版本和 ICU data 而略有不同，
    // 特别是时间部分的 AM/PM 或 24 小时制。下面的断言基于常见的中文环境。
    // 为了更稳定的测试，可以考虑固定时区或使用 date-fns-tz 等库。

    // 假设本地时区是中国标准时间 (CST, UTC+8)
    // 如果在不同时区运行，小时部分可能会不同。
    // 例如，'2024-07-15T10:30:00Z' 是 UTC 时间，转换为 CST 是 18:30
    // '2024-07-15T10:30:00' 会被 new Date() 解析为本地时间 10:30

    expect(formatDate('2024-07-15T10:30:00')).toMatch(/2024年7月15日(?: 上午10:30| 10:30)/);
    expect(formatDate('2023-01-05T00:00:00')).toMatch(/2023年1月5日(?: 上午12:00| 00:00)/);
    // 对于仅日期的字符串，new Date(str) 会解析为 UTC 午夜，然后 toLocaleDateString 会转为本地时区。
    // 为了测试稳定，最好提供完整的日期时间字符串，指定本地午夜。
    expect(formatDate('2022-12-25T00:00:00')).toMatch(/2022年12月25日(?: 上午12:00| 00:00)/);
  });

  it('应该处理不同月份和日期', () => {
    expect(formatDate('2024-03-01T08:00:00')).toMatch(/2024年3月1日(?: 上午08:00| 08:00)/);
    expect(formatDate('2024-11-30T23:59:59')).toMatch(/2024年11月30日(?: 下午11:59| 23:59)/);
  });

  it('对于无效日期字符串应该返回 Invalid Date 或类似结果', () => {
    // toLocaleDateString 对于无效日期的行为是返回 "Invalid Date"
    expect(formatDate('not-a-date')).toBe('Invalid Date');
    expect(formatDate('2024-13-01')).toBe('Invalid Date'); // 无效月份
    expect(formatDate('')).toBe('Invalid Date');
  });

  //  可以添加更多关于特定格式或边缘情况的测试
});

describe('debounce', () => {
  jest.useFakeTimers(); // 使用 Jest 的模拟计时器

  let mockFunc: jest.Mock;
  const waitTime = 500;

  beforeEach(() => {
    mockFunc = jest.fn(); // 在每个测试前重置模拟函数
    jest.clearAllTimers(); // 清除所有计时器
  });

  it('在等待时间内不应调用函数', () => {
    const debouncedFunc = debounce(mockFunc, waitTime);
    debouncedFunc();
    expect(mockFunc).not.toHaveBeenCalled();
  });

  it('等待时间过后应该调用函数一次', () => {
    const debouncedFunc = debounce(mockFunc, waitTime);
    debouncedFunc();
    jest.advanceTimersByTime(waitTime); // 快进时间
    expect(mockFunc).toHaveBeenCalledTimes(1);
  });

  it('在等待时间内多次调用，只应在最后一次调用后执行一次', () => {
    const debouncedFunc = debounce(mockFunc, waitTime);
    debouncedFunc(); // 第一次调用
    jest.advanceTimersByTime(waitTime / 2); // 时间过半
    debouncedFunc(); // 第二次调用，重置计时器
    jest.advanceTimersByTime(waitTime / 2); // 时间又过半，总时间等于 waitTime，但由于第二次调用，不应执行
    expect(mockFunc).not.toHaveBeenCalled();

    jest.advanceTimersByTime(waitTime / 2); // 完成第二次调用后的等待时间
    expect(mockFunc).toHaveBeenCalledTimes(1);
  });

  it('应该正确传递参数给被包装的函数', () => {
    const debouncedFunc = debounce(mockFunc, waitTime);
    const arg1 = 'test';
    const arg2 = 123;
    debouncedFunc(arg1, arg2);
    jest.advanceTimersByTime(waitTime);
    expect(mockFunc).toHaveBeenCalledWith(arg1, arg2);
  });

  it('多次连续调用应只触发最后一次', () => {
    const debouncedFunc = debounce(mockFunc, waitTime);
    for (let i = 0; i < 5; i++) {
      debouncedFunc(i);
      jest.advanceTimersByTime(waitTime / 10); // 快速连续调用
    }
    jest.advanceTimersByTime(waitTime); // 等待最后一个调用完成
    expect(mockFunc).toHaveBeenCalledTimes(1);
    expect(mockFunc).toHaveBeenCalledWith(4); // 最后一个参数是 4
  });

  it('如果包装的函数在内部再次调用 debounce 后的函数，行为应符合预期（尽管不推荐这种模式）', () => {
    const debouncedFunc = debounce(mockFunc, waitTime);
    mockFunc.mockImplementation(() => {
      // 模拟一个场景：函数执行后又触发了 debounce 函数，但不应该导致无限循环
      // 这个测试确保 debounce 逻辑的健壮性
    });

    debouncedFunc();
    jest.advanceTimersByTime(waitTime);
    expect(mockFunc).toHaveBeenCalledTimes(1);

    // 如果 mockFunc 内部直接调用 debouncedFunc，会形成递归，且计时器会被不断重置
    // 这里的测试是确保单次 debounce 行为正确
  });
});

describe('truncateText', () => {
  it('当文本长度小于等于最大长度时，应返回原始文本', () => {
    expect(truncateText('hello', 10)).toBe('hello');
    expect(truncateText('world', 5)).toBe('world');
    expect(truncateText('', 5)).toBe('');
  });

  it('当文本长度大于最大长度时，应截断文本并添加省略号', () => {
    expect(truncateText('hello world', 5)).toBe('hello...');
    
    const inputText = '这是一个长文本示例';
    const maxLength = 4;
    const expectedOutput = '这是一个...';
    const actualOutput = truncateText(inputText, maxLength);

    // 调试日志可以保留或移除，现在问题已找到
    // console.log('Input Text:', inputText);
    // console.log('Max Length:', maxLength);
    // console.log('Expected Output:', expectedOutput, 'Length:', expectedOutput.length, 'CharCodes:', Array.from(expectedOutput).map(c => c.charCodeAt(0)));
    // console.log('Actual Output:', actualOutput, 'Length:', actualOutput.length, 'CharCodes:', Array.from(actualOutput).map(c => c.charCodeAt(0)));
    // console.log('Slice result for debug:', inputText.slice(0, maxLength));

    expect(actualOutput).toBe(expectedOutput);
  });

  it('当最大长度为0时，应返回省略号', () => {
    expect(truncateText('hello', 0)).toBe('...');
  });

  it('当最大长度为负数时，函数当前行为是 slice(0, negativeVal)，然后加 ellipsis', () => {
    // 'hello'.slice(0, -1) 是 'hell'
    expect(truncateText('hello', -1)).toBe('hell...'); 
    // 函数可以改进为: if (maxLength < 0) maxLength = 0;
  });

  it('应正确处理包含特殊字符的文本', () => {
    // '你好，世界！'.length 是 6. slice(0, 5) 是 '你好，世界'
    expect(truncateText('你好，世界！', 5)).toBe('你好，世界...');
  });
}); 