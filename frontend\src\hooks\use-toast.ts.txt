// 第 1 行: 指令，表明这个文件及其导出的模块应该被视为客户端组件。这对于使用 React Hooks (如 useState, useEffect) 和处理浏览器事件至关重要。
'use client'

// 第 3 行: 导入整个 React 库，并将其命名为 React。这是使用 React 功能（如 Hooks 和 JSX 元素类型）所必需的。
import * as React from 'react'

// 第 5 行: 类型导入，从自定义的 toast UI 组件中导入类型定义。
import type {
  ToastActionElement, // 第 6 行: Toast 操作元素的类型，例如一个按钮。
  ToastProps, // 第 7 行: Toast 组件自身属性的类型。
} from '@/components/ui/toast' // 第 8 行: 指向项目中 UI 组件库的路径。

// 第 10 行: 常量，定义了屏幕上一次最多能显示的 toast 通知的数量。
const TOAST_LIMIT = 1
// 第 11 行: 常量，定义了 toast 在被标记为关闭后，延迟多久才真正从状态中移除（单位：毫秒）。这里设置了一个非常大的值，实际上可能意味着不会自动移除，除非手动调用。
const TOAST_REMOVE_DELAY = 1000000

// 第 13 行: 类型别名，定义了一个在 toast 系统内部使用的 toast 对象结构。
type ToasterToast = ToastProps & { // 第 13 行: 它扩展了基础的 ToastProps。
  id: string // 第 14 行: 每个 toast 的唯一标识符。
  title?: React.ReactNode // 第 15 行: toast 的标题，可以是任何 React 节点 (文本、组件等)。可选。
  description?: React.ReactNode // 第 16 行: toast 的描述内容。可选。
  action?: ToastActionElement // 第 17 行: toast 中可交互的动作元素，如撤销按钮。可选。
}

// 第 20 行: 对象，定义了 reducer 使用的 action 类型常量。使用 `as const` (const assertion) 使得 TypeScript 将这些属性推断为字面量类型，而不是通用的 string 类型。
const actionTypes = {
  ADD_TOAST: 'ADD_TOAST', // 第 21 行: 添加新的 toast。
  UPDATE_TOAST: 'UPDATE_TOAST', // 第 22 行: 更新已存在的 toast。
  DISMISS_TOAST: 'DISMISS_TOAST', // 第 23 行: 标记 toast 为关闭状态 (通常会触发关闭动画，但还未从状态中移除)。
  REMOVE_TOAST: 'REMOVE_TOAST', // 第 24 行: 从状态中彻底移除 toast。
} as const

// 第 27 行: 模块级变量，用于生成 toast 的唯一 ID。
let count = 0

// 第 29 行: 函数，用于生成一个唯一的 ID 字符串。
function genId() {
  count = (count + 1) % Number.MAX_VALUE // 第 30 行: count 自增并取模，以防止溢出 Number.MAX_VALUE。
  return count.toString() // 第 31 行: 将数字 ID 转换为字符串。
}

// 第 34 行: 类型别名，从 actionTypes 对象的值创建联合类型，代表所有可能的 action 类型字符串。
type ActionType = typeof actionTypes

// 第 36 行: 类型别名，定义了 reducer action 的联合类型。每个 action 对象都有一个 type 属性和与该类型相关的其他属性。
type Action =
  | { // 第 37 行: 添加 toast 的 action。
      type: ActionType['ADD_TOAST'] // 第 38 行: action 类型。
      toast: ToasterToast // 第 39 行: 要添加的 toast 对象。
    }
  | { // 第 41 行: 更新 toast 的 action。
      type: ActionType['UPDATE_TOAST'] // 第 42 行: action 类型。
      toast: Partial<ToasterToast> // 第 43 行: 要更新的 toast 属性 (可以是部分属性)。
      id: string // 第 44 行: 要更新的 toast 的 ID。
    }
  | { // 第 46 行: 关闭 toast 的 action。
      type: ActionType['DISMISS_TOAST'] // 第 47 行: action 类型。
      toastId?: string // 第 48 行: 要关闭的 toast 的 ID。如果未提供，则关闭所有 toast。可选。
    }
  | { // 第 50 行: 移除 toast 的 action。
      type: ActionType['REMOVE_TOAST'] // 第 51 行: action 类型。
      toastId?: string // 第 52 行: 要移除的 toast 的 ID。如果未提供，则移除所有 toast。可选。
    }

// 第 55 行: 接口，定义了 toast 状态对象的结构。
interface State {
  toasts: ToasterToast[] // 第 56 行: 一个包含当前所有活动 toast 对象的数组。
}

// 第 59 行: Map 对象，用于存储 toast ID 到其对应的 setTimeout 计时器的映射。这用于管理 toast 的自动移除。
const toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()

// 第 61 行: 函数，将一个 toast 加入到待移除队列中。
const addToRemoveQueue = (toastId: string) => {
  if (toastTimeouts.has(toastId)) { // 第 62 行: 如果该 toast 已经有一个移除计时器，则不重复添加。
    return // 第 63 行: 直接返回。
  }

  // 第 66 行: 设置一个定时器，在 TOAST_REMOVE_DELAY 毫秒后执行。
  const timeout = setTimeout(() => {
    toastTimeouts.delete(toastId) // 第 67 行: 从映射中删除该计时器。
    // 第 68 行: 派发一个 REMOVE_TOAST action 来实际移除 toast。
    dispatch({
      type: 'REMOVE_TOAST', // 第 69 行: action 类型。
      toastId: toastId, // 第 70 行: 要移除的 toast ID。
    })
  }, TOAST_REMOVE_DELAY) // 第 72 行: 延迟时间。

  toastTimeouts.set(toastId, timeout) // 第 74 行: 将新的计时器存入映射中。
}

// 第 76 行: 导出的 reducer 函数，负责处理 toast 状态的更新逻辑。
export const reducer = (state: State, action: Action): State => {
  switch (action.type) { // 第 77 行: 根据 action 的类型执行不同的操作。
    case 'ADD_TOAST': // 第 78 行: 处理添加 toast 的 action。
      return { // 第 79 行: 返回新的状态对象。
        ...state, // 第 80 行: 扩展旧状态。
        // 第 81 行: 将新的 toast (action.toast) 添加到 toasts 数组的开头，并使用 slice 确保数组长度不超过 TOAST_LIMIT。
        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),
      }

    case 'UPDATE_TOAST': // 第 84 行: 处理更新 toast 的 action。
      return { // 第 85 行: 返回新的状态对象。
        ...state, // 第 86 行: 扩展旧状态。
        // 第 87 行: 映射 toasts 数组，如果 toast 的 ID 匹配 action.id，则用 action.toast 中的属性更新该 toast。
        toasts: state.toasts.map((t) =>
          t.id === action.id ? { ...t, ...action.toast } : t
        ),
      }

    case 'DISMISS_TOAST': { // 第 93 行: 处理关闭 toast 的 action。
      const { toastId } = action // 第 94 行: 从 action 中获取 toastId。

      if (toastId) { // 第 96 行: 如果提供了 toastId，则只关闭指定的 toast。
        addToRemoveQueue(toastId) // 第 97 行: 将其加入移除队列。
      } else { // 第 98 行: 如果没有提供 toastId，则关闭所有当前显示的 toast。
        state.toasts.forEach((toast) => { // 第 99 行: 遍历所有 toast。
          addToRemoveQueue(toast.id) // 第 100 行: 将每个 toast 加入移除队列。
        })
      }

      return { // 第 104 行: 返回新的状态对象。
        ...state, // 第 105 行: 扩展旧状态。
        // 第 106 行: 映射 toasts 数组。
        toasts: state.toasts.map((t) =>
          // 第 107 行: 如果 toast ID 匹配或者没有指定 toastId (意味着关闭所有)。
          t.id === toastId || toastId === undefined
            ? { // 第 108 行: 则更新该 toast。
                ...t, // 第 109 行: 保留原有属性。
                open: false, // 第 110 行: 将 open 状态设置为 false，这通常会触发 UI 上的关闭动画。
              }
            : t // 第 112 行: 否则保持不变。
        ),
      }
    }

    case 'REMOVE_TOAST': // 第 116 行: 处理移除 toast 的 action。
      if (action.toastId === undefined) { // 第 117 行: 如果没有指定 toastId，则移除所有 toast。
        return { // 第 118 行: 返回新的状态对象。
          ...state, // 第 119 行: 扩展旧状态。
          toasts: [], // 第 120 行: 将 toasts 数组清空。
        }
      }
      return { // 第 123 行: 如果指定了 toastId，则只移除该 toast。
        ...state, // 第 124 行: 扩展旧状态。
        toasts: state.toasts.filter((t) => t.id !== action.toastId), // 第 125 行: 过滤掉 ID 匹配的 toast。
      }
  }
}

// 第 129 行: 模块级变量，存储当前 toast 的全局状态。初始为空数组。
let memoryState: State = { toasts: [] }
// 第 130 行: 模块级变量，存储所有监听 toast 状态变化的函数 (通常是 useToast Hook 中的 setState)。
let listeners: Array<(state: State) => void> = []

// 第 132 行: 函数，用于派发 action 来更新 toast 状态。
function dispatch(action: Action) {
  memoryState = reducer(memoryState, action) // 第 133 行: 调用 reducer 计算新的状态，并更新 memoryState。
  listeners.forEach((listener) => { // 第 134 行: 遍历所有监听器。
    listener(memoryState) // 第 135 行: 调用每个监听器函数，并传入新的状态，触发组件的重新渲染。
  })
}

// 第 139 行: 类型别名，定义了调用 `toast()` 函数时传入的参数类型，它省略了 `ToasterToast` 中的 `id` 属性，因为 ID 是内部生成的。
type Toast = Omit<ToasterToast, 'id'>

// 第 141 行: 函数，用于程序化地显示一个新的 toast。
function toast({ ...props }: Toast) { // 第 141 行: 接收一个 Toast 类型的对象作为参数。
  const id = genId() // 第 142 行: 生成一个唯一的 ID。

  // 第 144 行: 定义一个 update 函数，用于更新这个特定的 toast。
  const update = (props: ToasterToast) =>
    dispatch({ // 第 145 行: 派发 UPDATE_TOAST action。
      type: 'UPDATE_TOAST', // 第 146 行: action 类型。
      id, // 第 147 行: 当前 toast 的 ID。
      toast: props, // 第 148 行: 更新的属性。
    })
  // 第 150 行: 定义一个 dismiss 函数，用于关闭这个特定的 toast。
  const dismiss = () => dispatch({ type: 'DISMISS_TOAST', toastId: id })

  // 第 152 行: 构造完整的 toast 对象，准备添加到状态中。
  const toastPayload = {
    ...props, // 第 153 行: 扩展传入的属性。
    id, // 第 154 行: 设置生成的 ID。
    open: true, // 第 155 行: 初始状态为打开。
    onOpenChange: (open: boolean) => { // 第 156 行: 当 toast 的打开状态改变时 (通常由 UI 组件触发)。
      if (!open) dismiss() // 第 157 行: 如果状态变为未打开，则调用 dismiss 函数。
    },
  };

  // 第 161 行: 派发 ADD_TOAST action 来添加这个新的 toast。
  dispatch({
    type: 'ADD_TOAST', // 第 162 行: action 类型。
    toast: toastPayload, // 第 163 行: 要添加的 toast 对象。
  })

  // 第 166 行: 返回一个包含 ID 和控制函数 (dismiss, update) 的对象，允许外部代码控制这个 toast。
  return {
    id: id, // 第 167 行: toast 的 ID。
    dismiss, // 第 168 行: 关闭此 toast 的函数。
    update, // 第 169 行: 更新此 toast 的函数。
  }
}

// 第 173 行: 自定义 React Hook，用于在组件中使用 toast 系统。
function useToast() {
  // 第 174 行: 使用 React.useState 来管理组件本地的 toast 状态，初始值为全局的 memoryState。
  const [state, setState] = React.useState<State>(memoryState)

  // 第 176 行: 使用 React.useEffect 来处理副作用：订阅和取消订阅全局状态的更新。
  React.useEffect(() => {
    listeners.push(setState) // 第 177 行: 组件挂载时，将组件的 setState 函数添加到全局监听器数组中。
    return () => { // 第 178 行: 返回一个清理函数，在组件卸载时执行。
      const index = listeners.indexOf(setState) // 第 179 行: 找到当前组件的 setState 在监听器数组中的索引。
      if (index > -1) { // 第 180 行: 如果找到了。
        listeners.splice(index, 1) // 第 181 行: 从监听器数组中移除该 setState 函数，防止内存泄漏。
      }
    }
  }, []) // 第 184 行: 空依赖数组意味着这个 effect 只在组件挂载和卸载时运行一次。

  // 第 186 行: 返回一个对象，包含当前的 toast 状态、显示 toast 的函数以及关闭 toast 的函数。
  return {
    ...state, // 第 187 行: 扩展当前的 toast 状态 (即 toasts 数组)。
    toast, // 第 188 行: 暴露全局的 toast 函数，允许组件显示新的 toast。
    dismiss: (toastId?: string) => dispatch({ type: 'DISMISS_TOAST', toastId }), // 第 189 行: 暴露一个 dismiss 函数，允许组件关闭 toast (可指定 ID 或关闭所有)。
  }
}

// 第 193 行: 导出的函数，用于在测试环境中重置 toast 状态。
// 测试用的重置函数
export const resetToastStateForTesting = () => {
  memoryState = { toasts: [] } // 第 195 行: 重置全局状态。
  listeners = [] // 第 196 行: 清空监听器。
  count = 0 // 第 197 行: 如果 genId 依赖于 count，也需要重置 count。
}

// 第 200 行: 导出 useToast Hook 和 toast 函数，供应用的其他部分使用。
export { useToast, toast } 