'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ScrollArea } from '@/components/ui/scroll-area'
import { formatDate } from '@/lib/utils'
import { Note } from '@/types'
import { Search, Plus, FileText, Trash2, Calendar, Tag } from 'lucide-react'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"

interface NoteListProps {
  notes?: Note[]
  selectedNoteId?: string
  onSelectNote?: (note: Note) => void
  onCreateNote?: () => void
  onDeleteNote?: (note: Note) => void
}

export function NoteList({
  notes = [],// 笔记数据数组
  selectedNoteId,// 当前选中的笔记ID
  onSelectNote,// 选择笔记的回调函数
  onCreateNote,// 创建笔记的回调函数
  onDeleteNote, // 删除笔记的回调函数
}: NoteListProps) {
  const [searchTerm, setSearchTerm] = useState('')

  const filteredNotes = notes.filter(note =>
    note.title.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="flex flex-col h-full">
      {/* 搜索和新建区域 */}
      <div className="space-y-4 mb-6">
        {/* 搜索框 */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="搜索笔记..."
            className="w-full pl-10 pr-4 py-3 rounded-xl border border-gray-200/50 dark:border-gray-700/50 bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200 text-gray-700 dark:text-gray-200 placeholder:text-gray-400"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        
        {/* 新建按钮 */}
        <Button
          onClick={onCreateNote}
          className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white rounded-xl py-3 font-medium shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center gap-2"
        >
          <Plus className="w-5 h-5" />
          创建新笔记
        </Button>
      </div>

      {/* 笔记列表 */}
      <ScrollArea className="flex-1 scrollbar-thin">
        <div className="space-y-4">
          {filteredNotes.map((note) => (
            <Card
              key={note.id}
              className={`cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-1 bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm border-gray-200/50 dark:border-gray-700/50 rounded-xl ${
                selectedNoteId === note.id 
                  ? 'ring-2 ring-blue-500/50 border-blue-500/50 shadow-lg' 
                  : 'hover:border-gray-300/50 dark:hover:border-gray-600/50'
              }`}
              onClick={() => onSelectNote?.(note)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2 flex-1 min-w-0">
                    <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                      <FileText className="w-4 h-4 text-white" />
                    </div>
                    <CardTitle className="text-lg font-semibold text-gray-800 dark:text-gray-200 truncate">
                      {note.title}
                    </CardTitle>
                  </div>
                  {selectedNoteId === note.id && (
                    <div className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                  )}
                </div>
              </CardHeader>
              
              <CardContent className="pt-0 pb-4">
                {/* 笔记内容预览 */}
                <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mb-3 leading-relaxed">
                  {note.content || '暂无内容...'}
                </p>
                
                {/* 标签 */}
                {note.tags && note.tags.length > 0 && (
                  <div className="flex items-center gap-1 mb-3">
                    <Tag className="w-3 h-3 text-gray-400" />
                    <div className="flex flex-wrap gap-1">
                      {note.tags.slice(0, 2).map((tag, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-md"
                        >
                          {tag}
                        </span>
                      ))}
                      {note.tags.length > 2 && (
                        <span className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-md">
                          +{note.tags.length - 2}
                        </span>
                      )}
                    </div>
                  </div>
                )}
                
                {/* 底部信息 */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400">
                    <Calendar className="w-3 h-3" />
                    <span>{formatDate(note.updated_at)}</span>
                  </div>
                  {/* 删除按钮 - 带确认弹窗 */}
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 text-gray-400 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>确认删除笔记？</AlertDialogTitle>
                        <AlertDialogDescription>
                          你确定要删除笔记 "{note.title}" 吗？此操作无法撤销。
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel onClick={(e) => e.stopPropagation()}>
                          取消
                        </AlertDialogCancel>
                        <AlertDialogAction
                          onClick={(e) => {
                            e.stopPropagation()
                            onDeleteNote?.(note)
                          }}
                          className="bg-red-500 hover:bg-red-600"
                        >
                          删除
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </CardContent>
            </Card>
          ))}
          
          {/* 空状态 */}
          {filteredNotes.length === 0 && (
            <div className="text-center py-12">
              <div className="w-20 h-20 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <FileText className="w-10 h-10 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-600 dark:text-gray-400 mb-2">
                {searchTerm ? '没有找到匹配的笔记' : '还没有笔记'}
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-500 mb-4">
                {searchTerm ? '尝试使用不同的关键词搜索' : '点击上方按钮创建你的第一篇笔记'}
              </p>
              {!searchTerm && (
                <Button
                  onClick={onCreateNote}
                  variant="outline"
                  className="border-dashed border-gray-300 dark:border-gray-600 hover:border-blue-500 hover:text-blue-600 transition-colors"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  创建笔记
                </Button>
              )}
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  )
} 