/**
 * 全局错误处理中间件
 */
const errorMiddleware = (err, req, res, next) => {
    console.error(err.stack);

    // 默认错误
    let statusCode = 500;
    let message = '服务器内部错误';

    // 处理特定类型的错误
    if (err.name === 'ValidationError') {
        statusCode = 400;
        message = err.message;
    } else if (err.name === 'UnauthorizedError') {
        statusCode = 401;
        message = '未授权的访问';
    } else if (err.name === 'ForbiddenError') {
        statusCode = 403;
        message = '禁止访问';
    } else if (err.name === 'NotFoundError') {
        statusCode = 404;
        message = '资源不存在';
    }

    // 开发环境返回详细错误信息
    const error = process.env.NODE_ENV === 'development'
        ? {
            message,
            stack: err.stack,
            details: err.details || {}
        }
        : { message };

    res.status(statusCode).json({
        status: 'error',
        ...error
    });
};

module.exports = errorMiddleware; 