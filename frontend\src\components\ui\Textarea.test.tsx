import { render, screen, fireEvent } from '@testing-library/react';
import { Textarea } from './textarea';

describe('Textarea 组件', () => {
  it('应该正确渲染文本区域', () => {
    render(<Textarea placeholder="请输入内容" />);
    expect(screen.getByPlaceholderText('请输入内容')).toBeInTheDocument();
  });

  it('应该正确处理值的变化', () => {
    const handleChange = jest.fn();
    render(<Textarea onChange={handleChange} />);
    
    const textarea = screen.getByRole('textbox');
    fireEvent.change(textarea, { target: { value: '测试内容\n第二行' } });
    
    expect(handleChange).toHaveBeenCalled();
    expect(textarea).toHaveValue('测试内容\n第二行');
  });

  it('禁用状态下不应该可编辑', () => {
    render(<Textarea disabled />);
    const textarea = screen.getByRole('textbox');
    
    expect(textarea).toBeDisabled();
    expect(textarea).toHaveClass('disabled:cursor-not-allowed');
  });

  it('应该正确应用自定义类名', () => {
    const customClass = 'custom-class';
    render(<Textarea className={customClass} />);
    
    expect(screen.getByRole('textbox')).toHaveClass(customClass);
  });

  it('应该有正确的最小高度', () => {
    render(<Textarea />);
    const textarea = screen.getByRole('textbox');
    
    expect(textarea).toHaveClass('min-h-[80px]');
  });

  it('应该正确处理焦点事件', () => {
    const handleFocus = jest.fn();
    const handleBlur = jest.fn();
    
    render(<Textarea onFocus={handleFocus} onBlur={handleBlur} />);
    const textarea = screen.getByRole('textbox');
    
    fireEvent.focus(textarea);
    expect(handleFocus).toHaveBeenCalledTimes(1);
    
    fireEvent.blur(textarea);
    expect(handleBlur).toHaveBeenCalledTimes(1);
  });

  it('应该支持自动调整大小', () => {
    render(<Textarea rows={5} />);
    const textarea = screen.getByRole('textbox');
    
    expect(textarea).toHaveAttribute('rows', '5');
  });
}); 