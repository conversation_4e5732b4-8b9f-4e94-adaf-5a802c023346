'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Note } from '@/types'
import { Save, X, Edit3, Type, FileText, Tag, Clock, Sparkles, Plus } from 'lucide-react'

interface NoteEditorProps {
  note?: Note
  onSave?: (note: Partial<Note>) => void
  onCancel?: () => void
}

export function NoteEditor({ note, onSave, onCancel }: NoteEditorProps) {
  const [title, setTitle] = useState(note?.title || '')
  const [content, setContent] = useState(note?.content || '')
  const [tags, setTags] = useState<string[]>(note?.tags || [])
  const [tagInput, setTagInput] = useState('')
  const [isSaving, setIsSaving] = useState(false)
  const [wordCount, setWordCount] = useState(0)
 
  useEffect(() => {
    setTitle(note?.title || '')
    setContent(note?.content || '')
    setTags(note?.tags || [])
  }, [note])

  useEffect(() => {
    setWordCount(content.length)
  }, [content])

  const handleAddTag = () => {
    const trimmedTag = tagInput.trim()
    if (trimmedTag && !tags.includes(trimmedTag)) {
      setTags([...tags, trimmedTag])
      setTagInput('')
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove))
  }

  const handleTagInputKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleAddTag()
    }
  }

  const handleSave = async () => {
    if (!title.trim()) return

    setIsSaving(true)
    try {
      await onSave?.({
        id: note?.id,
        title: title.trim(),
        content: content.trim(),
        tags: tags,
      })
      if (!note) {
        setTitle('')
        setContent('')
        setTags([])
      }
    } finally {
      setIsSaving(false)
    }
  }

  const isEditing = !!note

  return (
    <Card className="h-full bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm border-gray-200/50 dark:border-gray-700/50 rounded-2xl shadow-lg">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className={`w-10 h-10 rounded-xl flex items-center justify-center ${
              isEditing 
                ? 'bg-gradient-to-br from-green-500 to-emerald-600' 
                : 'bg-gradient-to-br from-blue-500 to-purple-600'
            }`}>
              {isEditing ? <Edit3 className="w-5 h-5 text-white" /> : <FileText className="w-5 h-5 text-white" />}
            </div>
            <div>
              <CardTitle className="text-xl font-semibold text-gray-800 dark:text-gray-200">
                {isEditing ? '编辑笔记' : '创建笔记'}
              </CardTitle>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                {isEditing ? '修改你的想法' : '记录新的灵感'}
              </p>
            </div>
          </div>
          
          {/* 装饰元素 */}
          <div className="flex items-center gap-2">
            <Sparkles className="w-4 h-4 text-yellow-500 animate-pulse" />
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0 space-y-6">
        {/* 标题输入 */}
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300">
            <Type className="w-4 h-4" />
            <span>标题</span>
            <span className="text-red-500">*</span>
          </div>
          <Input
            placeholder="给你的笔记起个标题..."
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            className="border-gray-200/50 dark:border-gray-700/50 bg-white/50 dark:bg-gray-900/50 backdrop-blur-sm focus:border-blue-500/50 focus:ring-blue-500/50 rounded-xl py-3 text-gray-800 dark:text-gray-200 placeholder:text-gray-400 transition-all duration-200"
          />
        </div>

        {/* 内容输入 */}
        <div className="space-y-2 flex-1">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300">
              <FileText className="w-4 h-4" />
              <span>内容</span>
            </div>
            <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
              <Clock className="w-3 h-3" />
              <span>{wordCount} 字符</span>
            </div>
          </div>
          <Textarea
            placeholder="开始写下你的想法..."
            className="min-h-[300px] resize-none border-gray-200/50 dark:border-gray-700/50 bg-white/50 dark:bg-gray-900/50 backdrop-blur-sm focus:border-blue-500/50 focus:ring-blue-500/50 rounded-xl p-4 text-gray-800 dark:text-gray-200 placeholder:text-gray-400 transition-all duration-200 leading-relaxed"
            value={content}
            onChange={(e) => setContent(e.target.value)}
          />
        </div>

        {/* 标签输入区域 */}
        <div className="space-y-3">
          <div className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300">
            <Tag className="w-4 h-4" />
            <span>标签</span>
          </div>
          
          {/* 标签输入框 */}
          <div className="flex gap-2">
            <Input
              placeholder="输入标签后按回车添加..."
              value={tagInput}
              onChange={(e) => setTagInput(e.target.value)}
              onKeyPress={handleTagInputKeyPress}
              className="flex-1 border-gray-200/50 dark:border-gray-700/50 bg-white/50 dark:bg-gray-900/50 backdrop-blur-sm focus:border-blue-500/50 focus:ring-blue-500/50 rounded-xl py-2 text-gray-800 dark:text-gray-200 placeholder:text-gray-400 transition-all duration-200"
            />
            <Button
              type="button"
              onClick={handleAddTag}
              disabled={!tagInput.trim() || tags.includes(tagInput.trim())}
              size="sm"
              className="px-3 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-xl transition-all duration-200 flex items-center gap-1"
            >
              <Plus className="w-4 h-4" />
              添加
            </Button>
          </div>

          {/* 已添加的标签 */}
          {tags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {tags.map((tag, index) => (
                <span
                  key={index}
                  className="group px-3 py-1 text-sm bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-lg border border-blue-200/50 dark:border-blue-800/50 flex items-center gap-2 hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-all duration-200"
                >
                  {tag}
                  <button
                    onClick={() => handleRemoveTag(tag)}
                    className="opacity-60 hover:opacity-100 text-blue-600 dark:text-blue-400 hover:text-red-500 dark:hover:text-red-400 transition-all duration-200"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </span>
              ))}
            </div>
          )}

          {/* 标签提示 */}
          <p className="text-xs text-gray-500 dark:text-gray-400">
            💡 使用标签来分类和快速查找你的笔记
          </p>
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-end gap-3 pt-4 border-t border-gray-200/50 dark:border-gray-700/50">
          {onCancel && (
            <Button
              variant="outline"
              onClick={onCancel}
              disabled={isSaving}
              className="px-6 py-2 border-gray-200 dark:border-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-xl transition-all duration-200 flex items-center gap-2"
            >
              <X className="w-4 h-4" />
              取消
            </Button>
          )}
          <Button
            onClick={handleSave}
            disabled={!title.trim() || isSaving}
            className={`px-6 py-2 rounded-xl font-medium transition-all duration-200 flex items-center gap-2 ${
              title.trim()
                ? 'bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl'
                : 'bg-gray-200 dark:bg-gray-700 text-gray-400 cursor-not-allowed'
            }`}
          >
            <Save className="w-4 h-4" />
            {isSaving ? '保存中...' : '保存'}
          </Button>
        </div>

        {/* 提示信息 */}
        {!title.trim() && (
          <div className="text-sm text-amber-600 dark:text-amber-400 bg-amber-50 dark:bg-amber-900/20 rounded-lg p-3 border border-amber-200/50 dark:border-amber-800/50">
            💡 请输入笔记标题后再保存
          </div>
        )}
      </CardContent>
    </Card>
  )
} 