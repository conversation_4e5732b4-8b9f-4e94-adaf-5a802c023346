# Notes API 文档

## 基础信息

- 基础URL: `http://localhost:4000/api`
- 所有请求和响应均使用 JSON 格式
- 认证使用 Bearer Token 方式

## 认证相关接口

### 1. 用户注册

**请求**
- 方法: `POST`
- 路径: `/auth/register`
- Content-Type: `application/json`

**请求参数**
```json
{
    "email": "string",     // 必填，邮箱地址
    "password": "string",  // 必填，密码（至少6个字符）
    "name": "string"       // 可选，用户名（至少2个字符）
}
```

**响应**
- 状态码: 201
```json
{
    "status": "success",
    "data": {
        "user": {
            "id": "uuid",
            "email": "<EMAIL>",
            "name": "用户名",
            "created_at": "2024-03-15T10:00:00Z",
            "updated_at": "2024-03-15T10:00:00Z"
        },
        "token": "jwt_token"
    }
}
```

**错误响应**
- 状态码: 400
```json
{
    "status": "error",
    "errors": [
        {
            "msg": "请提供有效的邮箱地址",
            "param": "email",
            "location": "body"
        }
    ]
}
```

### 2. 用户登录

**请求**
- 方法: `POST`
- 路径: `/auth/login`
- Content-Type: `application/json`

**请求参数**
```json
{
    "email": "string",     // 必填，邮箱地址
    "password": "string"   // 必填，密码
}
```

**响应**
- 状态码: 200
```json
{
    "status": "success",
    "data": {
        "user": {
            "id": "uuid",
            "email": "<EMAIL>",
            "name": "用户名",
            "created_at": "2024-03-15T10:00:00Z",
            "updated_at": "2024-03-15T10:00:00Z"
        },
        "token": "jwt_token"
    }
}
```

**错误响应**
- 状态码: 401
```json
{
    "status": "error",
    "message": "用户不存在" // 或 "密码错误"
}
```

### 3. 获取当前用户信息

**请求**
- 方法: `GET`
- 路径: `/auth/me`
- Headers:
  - Authorization: `Bearer <token>`

**响应**
- 状态码: 200
```json
{
    "status": "success",
    "data": {
        "user": {
            "id": "uuid",
            "email": "<EMAIL>",
            "name": "用户名",
            "created_at": "2024-03-15T10:00:00Z",
            "updated_at": "2024-03-15T10:00:00Z"
        }
    }
}
```

**错误响应**
- 状态码: 401
```json
{
    "status": "error",
    "message": "未提供认证令牌" // 或 "无效的认证令牌"
}
```

## 笔记相关接口

### 1. 创建笔记

**请求**
- 方法: `POST`
- 路径: `/notes`
- Headers:
  - Authorization: `Bearer <token>`
- Content-Type: `application/json`

**请求参数**
```json
{
    "title": "string",      // 必填，笔记标题
    "content": "string",    // 必填，笔记内容
    "tags": ["string"]      // 可选，标签数组
}
```

**响应**
- 状态码: 201
```json
{
    "status": "success",
    "data": {
        "note": {
            "id": "uuid",
            "title": "笔记标题",
            "content": "笔记内容",
            "tags": ["标签1", "标签2"],
            "user_id": "uuid",
            "created_at": "2024-03-15T10:00:00Z",
            "updated_at": "2024-03-15T10:00:00Z"
        }
    }
}
```

### 2. 获取笔记列表

**请求**
- 方法: `GET`
- 路径: `/notes`
- Headers:
  - Authorization: `Bearer <token>`
- 查询参数:
  - page: 页码（可选，默认1）
  - limit: 每页数量（可选，默认10）
  - tag: 标签过滤（可选）
  - search: 搜索关键词（可选）

**响应**
- 状态码: 200
```json
{
    "status": "success",
    "data": {
        "notes": [
            {
                "id": "uuid",
                "title": "笔记标题",
                "content": "笔记内容",
                "tags": ["标签1", "标签2"],
                "user_id": "uuid",
                "created_at": "2024-03-15T10:00:00Z",
                "updated_at": "2024-03-15T10:00:00Z"
            }
        ],
        "pagination": {
            "total": 100,
            "page": 1,
            "limit": 10,
            "total_pages": 10
        }
    }
}
```

### 3. 获取单个笔记

**请求**
- 方法: `GET`
- 路径: `/notes/:id`
- Headers:
  - Authorization: `Bearer <token>`

**响应**
- 状态码: 200
```json
{
    "status": "success",
    "data": {
        "note": {
            "id": "uuid",
            "title": "笔记标题",
            "content": "笔记内容",
            "tags": ["标签1", "标签2"],
            "user_id": "uuid",
            "created_at": "2024-03-15T10:00:00Z",
            "updated_at": "2024-03-15T10:00:00Z"
        }
    }
}
```

### 4. 更新笔记

**请求**
- 方法: `PUT`
- 路径: `/notes/:id`
- Headers:
  - Authorization: `Bearer <token>`
- Content-Type: `application/json`

**请求参数**
```json
{
    "title": "string",      // 可选，笔记标题
    "content": "string",    // 可选，笔记内容
    "tags": ["string"]      // 可选，标签数组
}
```

**响应**
- 状态码: 200
```json
{
    "status": "success",
    "data": {
        "note": {
            "id": "uuid",
            "title": "更新后的标题",
            "content": "更新后的内容",
            "tags": ["标签1", "标签2"],
            "user_id": "uuid",
            "created_at": "2024-03-15T10:00:00Z",
            "updated_at": "2024-03-15T10:00:00Z"
        }
    }
}
```

### 5. 删除笔记

**请求**
- 方法: `DELETE`
- 路径: `/notes/:id`
- Headers:
  - Authorization: `Bearer <token>`

**响应**
- 状态码: 200
```json
{
    "status": "success",
    "data": {
        "message": "笔记已成功删除"
    }
}
```

### 6. 获取标签列表

**请求**
- 方法: `GET`
- 路径: `/notes/tags`
- Headers:
  - Authorization: `Bearer <token>`

**响应**
- 状态码: 200
```json
{
    "status": "success",
    "data": {
        "tags": [
            {
                "name": "标签1",
                "count": 10
            },
            {
                "name": "标签2",
                "count": 5
            }
        ]
    }
}
```

## 错误码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未认证或认证失败 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 通用响应格式

### 成功响应
```json
{
    "status": "success",
    "data": {
        // 具体的响应数据
    }
}
```

### 错误响应
```json
{
    "status": "error",
    "message": "错误信息",
    "errors": [] // 可选，详细错误信息
}
``` 