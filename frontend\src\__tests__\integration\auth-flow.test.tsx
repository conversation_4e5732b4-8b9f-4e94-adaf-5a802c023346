import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/store/auth'
import LoginPage from '@/app/(auth)/login/page'
import RegisterPage from '@/app/(auth)/register/page'

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}))

// Mock axios
jest.mock('axios', () => ({
  create: jest.fn(() => ({
    post: jest.fn(),
    get: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
    defaults: { headers: { common: {} } },
  })),
}))

// Mock auth store
jest.mock('@/store/auth', () => ({
  useAuthStore: jest.fn(),
}))

// Mock cookies
jest.mock('js-cookie', () => ({
  get: jest.fn(),
  set: jest.fn(),
  remove: jest.fn(),
}))

const mockPush = jest.fn()
const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>
const mockUseAuthStore = useAuthStore as jest.MockedFunction<typeof useAuthStore>

// Test wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe('认证流程集成测试', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockUseRouter.mockReturnValue({
      push: mockPush,
      replace: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
      prefetch: jest.fn(),
    } as any)
  })

  describe('登录流程', () => {
    beforeEach(() => {
      mockUseAuthStore.mockReturnValue({
        user: null,
        token: null,
        isAuthenticated: false,
        login: jest.fn(),
        logout: jest.fn(),
        register: jest.fn(),
        setUser: jest.fn(),
        clearAuth: jest.fn(),
      })
    })

    it('应该渲染登录表单', () => {
      render(
        <TestWrapper>
          <LoginPage />
        </TestWrapper>
      )

      expect(screen.getByRole('heading', { name: /欢迎回来/i })).toBeInTheDocument()
      expect(screen.getByLabelText(/邮箱/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/密码/i)).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /登录/i })).toBeInTheDocument()
    })

    it('应该显示表单验证错误', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <LoginPage />
        </TestWrapper>
      )

      const submitButton = screen.getByRole('button', { name: /登录/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText(/请输入有效的邮箱地址/i)).toBeInTheDocument()
        expect(screen.getByText(/密码至少需要6个字符/i)).toBeInTheDocument()
      })
    })

    it('应该能够输入邮箱和密码', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <LoginPage />
        </TestWrapper>
      )

      const emailInput = screen.getByLabelText(/邮箱/i)
      const passwordInput = screen.getByLabelText(/密码/i)

      await user.type(emailInput, '<EMAIL>')
      await user.type(passwordInput, 'password123')

      expect(emailInput).toHaveValue('<EMAIL>')
      expect(passwordInput).toHaveValue('password123')
    })

    it('应该处理登录成功的情况', async () => {
      const mockLogin = jest.fn().mockResolvedValue({
        user: { id: '1', email: '<EMAIL>' },
        token: 'mock-token'
      })

      mockUseAuthStore.mockReturnValue({
        user: null,
        token: null,
        isAuthenticated: false,
        login: mockLogin,
        logout: jest.fn(),
        register: jest.fn(),
        setUser: jest.fn(),
        clearAuth: jest.fn(),
      })

      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <LoginPage />
        </TestWrapper>
      )

      const emailInput = screen.getByLabelText(/邮箱/i)
      const passwordInput = screen.getByLabelText(/密码/i)
      const submitButton = screen.getByRole('button', { name: /登录/i })

      await user.type(emailInput, '<EMAIL>')
      await user.type(passwordInput, 'password123')
      await user.click(submitButton)

      await waitFor(() => {
        expect(mockLogin).toHaveBeenCalledWith('<EMAIL>', 'password123')
      })
    })
  })

  describe('注册流程', () => {
    beforeEach(() => {
      mockUseAuthStore.mockReturnValue({
        user: null,
        token: null,
        isAuthenticated: false,
        login: jest.fn(),
        logout: jest.fn(),
        register: jest.fn(),
        setUser: jest.fn(),
        clearAuth: jest.fn(),
      })
    })

    it('应该渲染注册表单', () => {
      render(
        <TestWrapper>
          <RegisterPage />
        </TestWrapper>
      )

      expect(screen.getByRole('heading', { name: /创建账号/i })).toBeInTheDocument()
      expect(screen.getByLabelText(/名字/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/邮箱/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/^密码$/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/确认密码/i)).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /注册/i })).toBeInTheDocument()
    })

    it('应该验证密码确认', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <RegisterPage />
        </TestWrapper>
      )

      const passwordInput = screen.getByLabelText(/^密码$/i)
      const confirmPasswordInput = screen.getByLabelText(/确认密码/i)
      const submitButton = screen.getByRole('button', { name: /注册/i })

      await user.type(passwordInput, 'password123')
      await user.type(confirmPasswordInput, 'differentpassword')
      await user.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText(/两次输入的密码不一致/i)).toBeInTheDocument()
      })
    })

    it('应该处理注册成功的情况', async () => {
      const mockRegister = jest.fn().mockResolvedValue({
        user: { id: '1', email: '<EMAIL>', name: 'Test User' },
        token: 'mock-token'
      })

      mockUseAuthStore.mockReturnValue({
        user: null,
        token: null,
        isAuthenticated: false,
        login: jest.fn(),
        logout: jest.fn(),
        register: mockRegister,
        setUser: jest.fn(),
        clearAuth: jest.fn(),
      })

      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <RegisterPage />
        </TestWrapper>
      )

      await user.type(screen.getByLabelText(/名字/i), 'Test User')
      await user.type(screen.getByLabelText(/邮箱/i), '<EMAIL>')
      await user.type(screen.getByLabelText(/^密码$/i), 'password123')
      await user.type(screen.getByLabelText(/确认密码/i), 'password123')
      await user.click(screen.getByRole('button', { name: /注册/i }))

      await waitFor(() => {
        expect(mockRegister).toHaveBeenCalledWith({
          name: 'Test User',
          email: '<EMAIL>',
          password: 'password123'
        })
      })
    })
  })

  describe('认证状态管理', () => {
    it('应该正确处理认证状态变化', () => {
      const mockUser = { id: '1', email: '<EMAIL>', name: 'Test User' }
      const mockToken = 'mock-token'

      // 测试未认证状态
      mockUseAuthStore.mockReturnValue({
        user: null,
        token: null,
        isAuthenticated: false,
        login: jest.fn(),
        logout: jest.fn(),
        register: jest.fn(),
        setUser: jest.fn(),
        clearAuth: jest.fn(),
      })

      const { rerender } = render(
        <TestWrapper>
          <div data-testid="auth-status">
            {mockUseAuthStore().isAuthenticated ? 'authenticated' : 'not-authenticated'}
          </div>
        </TestWrapper>
      )

      expect(screen.getByTestId('auth-status')).toHaveTextContent('not-authenticated')

      // 测试已认证状态
      mockUseAuthStore.mockReturnValue({
        user: mockUser,
        token: mockToken,
        isAuthenticated: true,
        login: jest.fn(),
        logout: jest.fn(),
        register: jest.fn(),
        setUser: jest.fn(),
        clearAuth: jest.fn(),
      })

      rerender(
        <TestWrapper>
          <div data-testid="auth-status">
            {mockUseAuthStore().isAuthenticated ? 'authenticated' : 'not-authenticated'}
          </div>
        </TestWrapper>
      )

      expect(screen.getByTestId('auth-status')).toHaveTextContent('authenticated')
    })
  })
}) 