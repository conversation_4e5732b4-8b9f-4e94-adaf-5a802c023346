const withBundleAnalyzer = require('@next/bundle-analyzer')({ // 第 1 行: 引入 '@next/bundle-analyzer' 包，并立即调用它返回的函数来创建一个高阶函数 withBundleAnalyzer。这个插件用于分析 webpack 打包后的文件大小和内容。
  enabled: process.env.ANALYZE === 'true', // 第 2 行: 配置 bundle analyzer 是否启用。它会检查环境变量 ANALYZE 是否严格等于字符串 'true'。
}) // 第 3 行: withBundleAnalyzer 初始化调用的结束。

// 第 4 行: 空行，用于可读性。
/** @type {import('next').NextConfig} */ // 第 5 行: JSDoc 类型注释，为 nextConfig 对象提供 TypeScript 类型提示，帮助开发者编写正确的配置结构。
const nextConfig = { // 第 6 行: 定义 Next.js 的主要配置对象 nextConfig。
  images: { // 第 7 行: 'images' 属性用于配置 Next.js 的图片优化功能 (next/image 组件)。
    domains: ['your-image-domain.com'], // 第 8 行: 'domains' 数组指定了允许从哪些外部域名加载并优化图片。注意：'your-image-domain.com' 是一个占位符，需要替换为实际使用的图片域名。
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840], // 第 9 行: 'deviceSizes' 数组定义了一组设备宽度断点。Next.js 会根据这些尺寸为响应式图片生成不同大小的版本。
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384], // 第 10 行: 'imageSizes' 数组定义了一组图片宽度。这主要用于 next/image 的 'sizes' 属性或当图片具有固定大小时，Next.js 会生成这些尺寸的图片。
    formats: ['image/webp'], // 第 11 行: 'formats' 数组指定了图片优化的输出格式。这里配置为 ['image/webp']，表示 Next.js 会尝试将图片转换为 WebP 格式 (如果浏览器支持)，因为它通常提供更好的压缩和质量。
  }, // 第 12 行: 'images' 配置对象的结束。
  compress: true, // 第 13 行: 'compress' 属性设置为 true (默认也是 true)，启用 Gzip 压缩服务端的响应和静态文件，以减小传输体积。
  experimental: { // 第 14 行: 'experimental' 对象用于开启和配置 Next.js 的实验性功能。
    optimizeCss: true, // 第 15 行: 'optimizeCss' 设置为 true，启用实验性的 CSS 优化功能。Next.js 会尝试优化项目中的 CSS。
    optimizePackageImports: ['@radix-ui/react-icons', 'lucide-react'], // 第 16 行: 'optimizePackageImports' 数组指定了哪些包的导入需要被特别优化，这有助于减少从这些库中引入不必要的代码，特别是对于图标库。
    scrollRestoration: true, // 第 17 行: 'scrollRestoration' 设置为 true，当用户使用浏览器的后退/前进按钮导航时，Next.js 将尝试恢复页面的滚动位置。
  }, // 第 18 行: 'experimental' 配置对象的结束。
} // 第 19 行: nextConfig 对象定义的结束。

// 第 20 行: 空行。
module.exports = withBundleAnalyzer(nextConfig) // 第 21 行: 导出最终的 Next.js 配置。nextConfig 对象被传递给 withBundleAnalyzer 高阶函数，该函数会包装原始配置以集成 bundle analyzer 的功能，然后返回修改后的配置供 Next.js 使用。