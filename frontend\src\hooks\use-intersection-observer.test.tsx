import { renderHook, act } from '@testing-library/react';
import { useIntersectionObserver } from './use-intersection-observer';
import React from 'react';

// 全局模拟 IntersectionObserver
const mockIntersectionObserver = jest.fn();
const mockObserve = jest.fn();
const mockUnobserve = jest.fn();
const mockDisconnect = jest.fn();

beforeAll(() => {
  // @ts-ignore
  global.IntersectionObserver = jest.fn((callback, options) => {
    mockIntersectionObserver(callback, options); // 允许我们断言构造函数被如何调用
    return {
      observe: mockObserve,
      unobserve: mockUnobserve,
      disconnect: mockDisconnect,
      root: options?.root || null,
      rootMargin: options?.rootMargin || '0px',
      thresholds: Array.isArray(options?.threshold) ? options.threshold : [options?.threshold || 0],
      takeRecords: jest.fn(() => []),
    };
  });
});

beforeEach(() => {
  // 在每个测试前重置所有模拟函数的调用记录
  mockIntersectionObserver.mockClear();
  mockObserve.mockClear();
  mockUnobserve.mockClear();
  mockDisconnect.mockClear();
});

// 辅助函数，用于触发模拟的 IntersectionObserver 的回调
const triggerIntersectionObserverCallback = (entry: Partial<IntersectionObserverEntry>) => {
  const callback = mockIntersectionObserver.mock.calls[0]?.[0];
  if (callback) {
    // @ts-ignore IntersectionObserverEntry is complex, partial mock is fine for tests
    callback([entry as IntersectionObserverEntry]);
  }
};

describe('useIntersectionObserver', () => {
  it('应该初始化 IntersectionObserver 并观察元素', () => {
    const mockElement = document.createElement('div');
    const elementRef = React.createRef<Element>();
    // @ts-ignore
    elementRef.current = mockElement;

    renderHook(() => useIntersectionObserver(elementRef, { threshold: 0.5 }));

    expect(mockIntersectionObserver).toHaveBeenCalledTimes(1);
    expect(mockIntersectionObserver).toHaveBeenCalledWith(
      expect.any(Function), // 回调函数
      { threshold: 0.5, root: null, rootMargin: '0%' } // 选项
    );
    expect(mockObserve).toHaveBeenCalledTimes(1);
    expect(mockObserve).toHaveBeenCalledWith(mockElement);
  });

  it('当元素交叉状态改变时应该更新 entry', () => {
    const mockElement = document.createElement('div');
    const elementRef = React.createRef<Element>();
    // @ts-ignore
    elementRef.current = mockElement;

    const { result } = renderHook(() => useIntersectionObserver(elementRef));

    // 初始 entry 应该是 undefined
    expect(result.current).toBeUndefined();

    // 模拟元素进入视口
    const testEntryVisible: Partial<IntersectionObserverEntry> = {
      isIntersecting: true,
      intersectionRatio: 0.8,
      target: mockElement,
    };
    act(() => {
      triggerIntersectionObserverCallback(testEntryVisible);
    });

    expect(result.current?.isIntersecting).toBe(true);
    expect(result.current?.intersectionRatio).toBe(0.8);
    expect(result.current?.target).toBe(mockElement);

    // 模拟元素离开视口
    const testEntryNotVisible: Partial<IntersectionObserverEntry> = {
      isIntersecting: false,
      intersectionRatio: 0,
      target: mockElement,
    };
    act(() => {
      triggerIntersectionObserverCallback(testEntryNotVisible);
    });

    expect(result.current?.isIntersecting).toBe(false);
    expect(result.current?.intersectionRatio).toBe(0);
  });

  it('当组件卸载时应该断开 observer', () => {
    const mockElement = document.createElement('div');
    const elementRef = React.createRef<Element>();
    // @ts-ignore
    elementRef.current = mockElement;

    const { unmount } = renderHook(() => useIntersectionObserver(elementRef));

    expect(mockObserve).toHaveBeenCalledWith(mockElement);

    unmount();

    expect(mockDisconnect).toHaveBeenCalledTimes(1);
  });

  it('如果 elementRef.current 为空，则不应初始化 observer', () => {
    const elementRef = React.createRef<Element>(); // current is null

    renderHook(() => useIntersectionObserver(elementRef));

    expect(mockIntersectionObserver).not.toHaveBeenCalled();
    expect(mockObserve).not.toHaveBeenCalled();
  });

  it('当依赖项（如 threshold）改变时应重新创建 observer', () => {
    const mockElement = document.createElement('div');
    const elementRef = React.createRef<Element>();
    // @ts-ignore
    elementRef.current = mockElement;

    const initialProps = { threshold: 0.1 };
    const { rerender } = renderHook(
      ({ threshold }) => useIntersectionObserver(elementRef, { threshold }),
      { initialProps }
    );

    expect(mockIntersectionObserver).toHaveBeenCalledTimes(1);
    expect(mockIntersectionObserver.mock.calls[0][1].threshold).toBe(0.1);
    expect(mockObserve).toHaveBeenCalledTimes(1);
    expect(mockDisconnect).not.toHaveBeenCalled(); // 初始时不应断开

    // 更改 threshold
    rerender({ threshold: 0.7 });

    expect(mockDisconnect).toHaveBeenCalledTimes(1); // 旧的 observer 应已断开
    expect(mockIntersectionObserver).toHaveBeenCalledTimes(2); // 新的 observer 应已创建
    expect(mockIntersectionObserver.mock.calls[1][1].threshold).toBe(0.7);
    expect(mockObserve).toHaveBeenCalledTimes(2); // 新的 observer 应已观察
  });

  it('应该使用默认选项，如果未提供', () => {
    const mockElement = document.createElement('div');
    const elementRef = React.createRef<Element>();
    // @ts-ignore
    elementRef.current = mockElement;

    renderHook(() => useIntersectionObserver(elementRef));

    expect(mockIntersectionObserver).toHaveBeenCalledWith(
      expect.any(Function),
      { threshold: 0, root: null, rootMargin: '0%' } // 默认值
    );
  });
}); 