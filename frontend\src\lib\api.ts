import axios from 'axios'
import { Note, User, NotesResponse } from '@/types'

const instance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

instance.interceptors.request.use((config) => {
  const token = localStorage.getItem('token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

interface GetNotesParams {
  page?: number
  limit?: number
  tag?: string
  search?: string
}

export const api = {
  auth: {
    login: async (email: string, password: string) => {
      return instance.post('/auth/login', { email, password })
    },
    register: async (email: string, password: string, name: string) => {
      return instance.post('/auth/register', { email, password, name })
    },
    logout: async () => {
      return instance.post('/auth/logout')
    },
    getProfile: async () => {
      return instance.get<User>('/auth/me')
    },
  },
  notes: {
    getAll: async (params: GetNotesParams = {}): Promise<NotesResponse> => {
      const { data } = await instance.get('/notes', { params })
      return data
    },
    getById: async (id: string): Promise<Note> => {
      const { data } = await instance.get(`/notes/${id}`)
      return data
    },
    create: async (note: Omit<Note, 'id' | 'created_at' | 'updated_at'>): Promise<Note> => {
      const { data } = await instance.post('/notes', note)
      return data
    },
    update: async (id: string, note: Partial<Note>): Promise<Note> => {
      const { data } = await instance.put(`/notes/${id}`, note)
      return data
    },
    delete: async (id: string): Promise<void> => {
      await instance.delete(`/notes/${id}`)
    },
    getTags: async (): Promise<string[]> => {
      const { data } = await instance.get('/notes/tags')
      return data
    },
  },
} 