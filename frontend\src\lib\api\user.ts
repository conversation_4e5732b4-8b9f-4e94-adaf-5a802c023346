import { axiosInstance as api } from '@/lib/axios'

export interface User {
  id: string
  email: string
  name: string
  avatar?: string
  created_at: string
  updated_at: string
}

// 由于axios拦截器返回response.data，这里的接口直接对应后端的响应格式
export interface UserResponse {
  status: 'success'
  data: {
    user: User
  }
}

/**
 * 获取当前用户信息
 */
export async function getCurrentUser(): Promise<User> {
  try {
    const response = await api.get<UserResponse>('/auth/me')
    // axios拦截器在运行时返回response.data，所以response实际上是UserResponse类型
    const data = response as unknown as UserResponse
    return data.data.user
  } catch (error) {
    console.error('获取用户信息失败:', error)
    throw error
  }
}

/**
 * 更新用户信息
 */
export async function updateUser(userData: Partial<Pick<User, 'name' | 'email'>>): Promise<User> {
  try {
    const response = await api.put<UserResponse>('/auth/me', userData)
    // axios拦截器在运行时返回response.data，所以response实际上是UserResponse类型
    const data = response as unknown as UserResponse
    return data.data.user
  } catch (error) {
    console.error('更新用户信息失败:', error)
    throw error
  }
} 