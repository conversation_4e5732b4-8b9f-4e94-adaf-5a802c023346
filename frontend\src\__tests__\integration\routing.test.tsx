import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { useRouter, usePathname } from 'next/navigation'
import { useAuthStore } from '@/store/auth'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import ProtectedLayout from '@/app/(protected)/layout'
import AuthLayout from '@/app/(auth)/layout'
import HomePage from '@/app/page'

// Mock Next.js router and navigation
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  usePathname: jest.fn(),
  useSearchParams: jest.fn(() => new URLSearchParams()),
  redirect: jest.fn(),
}))

// Mock middleware
jest.mock('@/middleware', () => ({
  middleware: jest.fn(),
}))

// Mock auth store
jest.mock('@/store/auth', () => ({
  useAuthStore: jest.fn(),
}))

// Mock cookies
jest.mock('js-cookie', () => ({
  get: jest.fn(),
  set: jest.fn(),
  remove: jest.fn(),
}))

const mockPush = jest.fn()
const mockReplace = jest.fn()
const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>
const mockUsePathname = usePathname as jest.MockedFunction<typeof usePathname>
const mockUseAuthStore = useAuthStore as jest.MockedFunction<typeof useAuthStore>

// Test wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe('路由和导航集成测试', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    
    mockUseRouter.mockReturnValue({
      push: mockPush,
      replace: mockReplace,
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
      prefetch: jest.fn(),
    } as any)
  })

  describe('公共路由', () => {
    beforeEach(() => {
      mockUseAuthStore.mockReturnValue({
        user: null,
        token: null,
        isAuthenticated: false,
        login: jest.fn(),
        logout: jest.fn(),
        register: jest.fn(),
        setUser: jest.fn(),
        clearAuth: jest.fn(),
      })
      
      mockUsePathname.mockReturnValue('/')
    })

    it('应该允许未认证用户访问首页', () => {
      render(
        <TestWrapper>
          <HomePage />
        </TestWrapper>
      )

      expect(screen.getByText(/欢迎使用笔记应用/i)).toBeInTheDocument()
    })

    it('应该在首页显示登录和注册链接', () => {
      render(
        <TestWrapper>
          <HomePage />
        </TestWrapper>
      )

      expect(screen.getByRole('link', { name: /登录/i })).toBeInTheDocument()
      expect(screen.getByRole('link', { name: /注册/i })).toBeInTheDocument()
    })

    it('已认证用户访问首页时应该重定向到笔记页面', () => {
      mockUseAuthStore.mockReturnValue({
        user: { id: '1', email: '<EMAIL>', name: 'Test User' },
        token: 'mock-token',
        isAuthenticated: true,
        login: jest.fn(),
        logout: jest.fn(),
        register: jest.fn(),
        setUser: jest.fn(),
        clearAuth: jest.fn(),
      })

      render(
        <TestWrapper>
          <HomePage />
        </TestWrapper>
      )

      expect(mockPush).toHaveBeenCalledWith('/notes')
    })
  })

  describe('认证路由', () => {
    beforeEach(() => {
      mockUseAuthStore.mockReturnValue({
        user: null,
        token: null,
        isAuthenticated: false,
        login: jest.fn(),
        logout: jest.fn(),
        register: jest.fn(),
        setUser: jest.fn(),
        clearAuth: jest.fn(),
      })
    })

    it('应该允许未认证用户访问登录页面', () => {
      mockUsePathname.mockReturnValue('/login')
      
      render(
        <TestWrapper>
          <AuthLayout>
            <div>登录页面内容</div>
          </AuthLayout>
        </TestWrapper>
      )

      expect(screen.getByText('登录页面内容')).toBeInTheDocument()
    })

    it('应该允许未认证用户访问注册页面', () => {
      mockUsePathname.mockReturnValue('/register')
      
      render(
        <TestWrapper>
          <AuthLayout>
            <div>注册页面内容</div>
          </AuthLayout>
        </TestWrapper>
      )

      expect(screen.getByText('注册页面内容')).toBeInTheDocument()
    })

    it('已认证用户访问登录页面时应该重定向', () => {
      mockUseAuthStore.mockReturnValue({
        user: { id: '1', email: '<EMAIL>', name: 'Test User' },
        token: 'mock-token',
        isAuthenticated: true,
        login: jest.fn(),
        logout: jest.fn(),
        register: jest.fn(),
        setUser: jest.fn(),
        clearAuth: jest.fn(),
      })

      mockUsePathname.mockReturnValue('/login')
      
      render(
        <TestWrapper>
          <AuthLayout>
            <div>登录页面内容</div>
          </AuthLayout>
        </TestWrapper>
      )

      expect(mockPush).toHaveBeenCalledWith('/notes')
    })
  })

  describe('受保护的路由', () => {
    it('应该允许已认证用户访问笔记页面', () => {
      mockUseAuthStore.mockReturnValue({
        user: { id: '1', email: '<EMAIL>', name: 'Test User' },
        token: 'mock-token',
        isAuthenticated: true,
        login: jest.fn(),
        logout: jest.fn(),
        register: jest.fn(),
        setUser: jest.fn(),
        clearAuth: jest.fn(),
      })

      mockUsePathname.mockReturnValue('/notes')
      
      render(
        <TestWrapper>
          <ProtectedLayout>
            <div>笔记页面内容</div>
          </ProtectedLayout>
        </TestWrapper>
      )

      expect(screen.getByText('笔记页面内容')).toBeInTheDocument()
    })

    it('未认证用户访问受保护页面时应该重定向到登录页面', () => {
      mockUseAuthStore.mockReturnValue({
        user: null,
        token: null,
        isAuthenticated: false,
        login: jest.fn(),
        logout: jest.fn(),
        register: jest.fn(),
        setUser: jest.fn(),
        clearAuth: jest.fn(),
      })

      mockUsePathname.mockReturnValue('/notes')
      
      render(
        <TestWrapper>
          <ProtectedLayout>
            <div>笔记页面内容</div>
          </ProtectedLayout>
        </TestWrapper>
      )

      expect(mockPush).toHaveBeenCalledWith('/login')
    })

    it('应该在受保护的布局中显示导航栏', () => {
      mockUseAuthStore.mockReturnValue({
        user: { id: '1', email: '<EMAIL>', name: 'Test User' },
        token: 'mock-token',
        isAuthenticated: true,
        login: jest.fn(),
        logout: jest.fn(),
        register: jest.fn(),
        setUser: jest.fn(),
        clearAuth: jest.fn(),
      })

      mockUsePathname.mockReturnValue('/notes')
      
      render(
        <TestWrapper>
          <ProtectedLayout>
            <div>笔记页面内容</div>
          </ProtectedLayout>
        </TestWrapper>
      )

      expect(screen.getByRole('navigation')).toBeInTheDocument()
    })
  })

  describe('导航功能', () => {
    beforeEach(() => {
      mockUseAuthStore.mockReturnValue({
        user: { id: '1', email: '<EMAIL>', name: 'Test User' },
        token: 'mock-token',
        isAuthenticated: true,
        login: jest.fn(),
        logout: jest.fn(),
        register: jest.fn(),
        setUser: jest.fn(),
        clearAuth: jest.fn(),
      })

      mockUsePathname.mockReturnValue('/notes')
    })

    it('应该能够通过导航栏退出登录', async () => {
      const mockLogout = jest.fn()
      
      mockUseAuthStore.mockReturnValue({
        user: { id: '1', email: '<EMAIL>', name: 'Test User' },
        token: 'mock-token',
        isAuthenticated: true,
        login: jest.fn(),
        logout: mockLogout,
        register: jest.fn(),
        setUser: jest.fn(),
        clearAuth: jest.fn(),
      })

      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <ProtectedLayout>
            <div>笔记页面内容</div>
          </ProtectedLayout>
        </TestWrapper>
      )

      // 点击用户菜单
      const userMenu = screen.getByRole('button', { name: /Test User/i })
      await user.click(userMenu)

      // 点击退出登录
      const logoutButton = screen.getByRole('menuitem', { name: /退出登录/i })
      await user.click(logoutButton)

      expect(mockLogout).toHaveBeenCalled()
    })

    it('应该能够在不同页面间导航', async () => {
      const user = userEvent.setup()
      
      render(
        <TestWrapper>
          <ProtectedLayout>
            <div>笔记页面内容</div>
          </ProtectedLayout>
        </TestWrapper>
      )

      // 点击导航链接
      const settingsLink = screen.getByRole('link', { name: /设置/i })
      await user.click(settingsLink)

      expect(mockPush).toHaveBeenCalledWith('/settings')
    })

    it('应该显示当前页面的面包屑导航', () => {
      mockUsePathname.mockReturnValue('/notes/edit/123')
      
      render(
        <TestWrapper>
          <ProtectedLayout>
            <div>编辑笔记页面</div>
          </ProtectedLayout>
        </TestWrapper>
      )

      expect(screen.getByText('首页')).toBeInTheDocument()
      expect(screen.getByText('笔记')).toBeInTheDocument()
      expect(screen.getByText('编辑')).toBeInTheDocument()
    })
  })

  describe('错误处理', () => {
    it('应该处理无效的路由', () => {
      mockUsePathname.mockReturnValue('/invalid-route')
      
      render(
        <TestWrapper>
          <div>404 页面</div>
        </TestWrapper>
      )

      expect(screen.getByText('404 页面')).toBeInTheDocument()
    })

    it('应该处理认证状态变化', async () => {
      // 初始状态：已认证
      mockUseAuthStore.mockReturnValue({
        user: { id: '1', email: '<EMAIL>', name: 'Test User' },
        token: 'mock-token',
        isAuthenticated: true,
        login: jest.fn(),
        logout: jest.fn(),
        register: jest.fn(),
        setUser: jest.fn(),
        clearAuth: jest.fn(),
      })

      const { rerender } = render(
        <TestWrapper>
          <ProtectedLayout>
            <div>受保护的内容</div>
          </ProtectedLayout>
        </TestWrapper>
      )

      expect(screen.getByText('受保护的内容')).toBeInTheDocument()

      // 状态变化：未认证
      mockUseAuthStore.mockReturnValue({
        user: null,
        token: null,
        isAuthenticated: false,
        login: jest.fn(),
        logout: jest.fn(),
        register: jest.fn(),
        setUser: jest.fn(),
        clearAuth: jest.fn(),
      })

      rerender(
        <TestWrapper>
          <ProtectedLayout>
            <div>受保护的内容</div>
          </ProtectedLayout>
        </TestWrapper>
      )

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/login')
      })
    })
  })

  describe('路由中间件', () => {
    it('应该在访问受保护路由前检查认证状态', () => {
      const request = new Request('http://localhost:3000/notes')
      
      // 这里会测试中间件的逻辑
      // 由于中间件在服务器端运行，我们主要测试其逻辑
      expect(request.url).toContain('/notes')
    })

    it('应该正确设置响应头', () => {
      const response = new Response('test')
      response.headers.set('x-pathname', '/notes')
      
      expect(response.headers.get('x-pathname')).toBe('/notes')
    })
  })
}) 