'use client' // 第 1 行: Next.js 指令，表明这个模块是客户端代码。操作 Cookies 必须在客户端进行。

// 第 2 行: 空行，用于可读性。
import Cookies from 'js-cookie' // 第 3 行: 从 'js-cookie' 库导入 Cookies 对象。js-cookie 是一个流行的用于处理浏览器 cookie 的 JavaScript 库。

// 第 4 行: 空行。
const TOKEN_NAME = 'token' // 第 5 行: 定义一个常量 TOKEN_NAME，值为 'token'。这个字符串将用作存储主认证令牌的 cookie 名称。
const REFRESH_TOKEN_NAME = 'refreshToken' // 第 6 行: 定义一个常量 REFRESH_TOKEN_NAME，值为 'refreshToken'。这个字符串将用作存储刷新令牌的 cookie 名称。

// 第 7 行: 空行。
export function getToken(): string | undefined { // 第 8 行: 定义并导出一个名为 getToken 的函数。此函数用于获取主认证令牌。返回类型是字符串或 undefined (如果 cookie 不存在)。
  return Cookies.get(TOKEN_NAME) // 第 9 行: 使用 Cookies.get() 方法从浏览器 cookie 中读取名为 TOKEN_NAME 的 cookie 值并返回。
} // 第 10 行: getToken 函数定义的结束。

// 第 11 行: 空行。
export function setToken(token: string): void { // 第 12 行: 定义并导出一个名为 setToken 的函数。此函数用于设置主认证令牌。它接受一个字符串类型的 token 参数，并且没有返回值 (void)。
  Cookies.set(TOKEN_NAME, token, { expires: 7 }) // 第 13 行: 使用 Cookies.set() 方法将 token 值存入名为 TOKEN_NAME 的 cookie。第三个参数是一个配置对象，{ expires: 7 } 表示此 cookie 将在 7 天后过期。
} // 第 14 行: setToken 函数定义的结束。

// 第 15 行: 空行。
export function removeToken(): void { // 第 16 行: 定义并导出一个名为 removeToken 的函数。此函数用于移除主认证令牌。没有返回值。
  Cookies.remove(TOKEN_NAME) // 第 17 行: 使用 Cookies.remove() 方法从浏览器中删除名为 TOKEN_NAME 的 cookie。
} // 第 18 行: removeToken 函数定义的结束。

// 第 19 行: 空行。
export function getRefreshToken(): string | undefined { // 第 20 行: 定义并导出一个名为 getRefreshToken 的函数。此函数用于获取刷新令牌。返回类型是字符串或 undefined。
  return Cookies.get(REFRESH_TOKEN_NAME) // 第 21 行: 使用 Cookies.get() 方法读取名为 REFRESH_TOKEN_NAME 的 cookie 值并返回。
} // 第 22 行: getRefreshToken 函数定义的结束。

// 第 23 行: 空行。
export function setRefreshToken(token: string): void { // 第 24 行: 定义并导出一个名为 setRefreshToken 的函数。此函数用于设置刷新令牌。它接受一个字符串类型的 token 参数，没有返回值。
  Cookies.set(REFRESH_TOKEN_NAME, token, { expires: 30 }) // 第 25 行: 使用 Cookies.set() 方法将 token 值存入名为 REFRESH_TOKEN_NAME 的 cookie。{ expires: 30 } 表示此 cookie 将在 30 天后过期。
} // 第 26 行: setRefreshToken 函数定义的结束。

// 第 27 行: 空行。
export function removeRefreshToken(): void { // 第 28 行: 定义并导出一个名为 removeRefreshToken 的函数。此函数用于移除刷新令牌。没有返回值。
  Cookies.remove(REFRESH_TOKEN_NAME) // 第 29 行: 使用 Cookies.remove() 方法从浏览器中删除名为 REFRESH_TOKEN_NAME 的 cookie。
} // 第 30 行: removeRefreshToken 函数定义的结束。 