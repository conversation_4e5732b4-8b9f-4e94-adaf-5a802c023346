import Cookies from 'js-cookie';
import {
  getToken,
  setToken,
  removeToken,
  getRefreshToken,
  setRefreshToken,
  removeRefreshToken,
} from './cookies';

// 模拟 js-cookie 模块
jest.mock('js-cookie', () => ({
  get: jest.fn(),
  set: jest.fn(),
  remove: jest.fn(),
}));

const TOKEN_NAME = 'token';
const REFRESH_TOKEN_NAME = 'refreshToken';

describe('Cookie Functions', () => {
  beforeEach(() => {
    // 在每个测试前清除所有模拟函数的调用记录
    (Cookies.get as jest.Mock).mockClear();
    (Cookies.set as jest.Mock).mockClear();
    (Cookies.remove as jest.Mock).mockClear();
  });

  describe('getToken', () => {
    it('应该调用 Cookies.get 获取 token', () => {
      (Cookies.get as jest.Mock).mockReturnValueOnce('test-token');
      const token = getToken();
      expect(Cookies.get).toHaveBeenCalledWith(TOKEN_NAME);
      expect(token).toBe('test-token');
    });
  });

  describe('setToken', () => {
    it('应该调用 Cookies.set 设置 token，有效期7天', () => {
      setToken('new-token');
      expect(Cookies.set).toHaveBeenCalledWith(TOKEN_NAME, 'new-token', { expires: 7 });
    });
  });

  describe('removeToken', () => {
    it('应该调用 Cookies.remove 移除 token', () => {
      removeToken();
      expect(Cookies.remove).toHaveBeenCalledWith(TOKEN_NAME);
    });
  });

  describe('getRefreshToken', () => {
    it('应该调用 Cookies.get 获取 refreshToken', () => {
      (Cookies.get as jest.Mock).mockReturnValueOnce('test-refresh-token');
      const token = getRefreshToken();
      expect(Cookies.get).toHaveBeenCalledWith(REFRESH_TOKEN_NAME);
      expect(token).toBe('test-refresh-token');
    });
  });

  describe('setRefreshToken', () => {
    it('应该调用 Cookies.set 设置 refreshToken，有效期30天', () => {
      setRefreshToken('new-refresh-token');
      expect(Cookies.set).toHaveBeenCalledWith(REFRESH_TOKEN_NAME, 'new-refresh-token', { expires: 30 });
    });
  });

  describe('removeRefreshToken', () => {
    it('应该调用 Cookies.remove 移除 refreshToken', () => {
      removeRefreshToken();
      expect(Cookies.remove).toHaveBeenCalledWith(REFRESH_TOKEN_NAME);
    });
  });
}); 