const supabase = require('../config/database');

async function testConnection() {
    try {
        // 尝试查询 users 表
        const { data, error } = await supabase
            .from('users')
            .select('count')
            .limit(1);

        if (error) {
            console.error('数据库连接测试失败:', error.message);
            return;
        }

        console.log('数据库连接测试成功!');
        console.log('当前配置:');
        console.log('- SUPABASE_URL:', process.env.SUPABASE_URL);
        console.log('- NODE_ENV:', process.env.NODE_ENV);
        console.log('- PORT:', process.env.PORT);
    } catch (error) {
        console.error('测试过程中出现错误:', error.message);
    }
}

testConnection(); 