import { create } from 'zustand'
import { Note } from '@/types'

interface NotesState {
  selectedNote: Note | null
  searchQuery: string
  selectedTags: string[]
  setSelectedNote: (note: Note | null) => void
  setSearchQuery: (query: string) => void
  setSelectedTags: (tags: string[]) => void
  addSelectedTag: (tag: string) => void
  removeSelectedTag: (tag: string) => void
  clearFilters: () => void
  clearSelectedTags: () => void
}

export const useNotesStore = create<NotesState>((set) => ({
  selectedNote: null,
  searchQuery: '',
  selectedTags: [],
  setSelectedNote: (note) => set({ selectedNote: note }),
  setSearchQuery: (query) => set({ searchQuery: query }),
  setSelectedTags: (tags) => set({ selectedTags: tags }),
  addSelectedTag: (tag) =>
    set((state) => ({
      selectedTags: state.selectedTags.includes(tag)
        ? state.selectedTags
        : [...state.selectedTags, tag],
    })),
  removeSelectedTag: (tag) =>
    set((state) => ({
      selectedTags: state.selectedTags.filter((t) => t !== tag),
    })),
  clearFilters: () => set({ searchQuery: '', selectedTags: [] }),
  clearSelectedTags: () => set({ selectedTags: [] }),
})) 